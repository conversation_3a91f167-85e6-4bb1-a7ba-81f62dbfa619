#!/usr/bin/env python3
"""
✅ Demo Validation Script
========================

Quick validation that all demo components are working correctly.
"""

import sys
import os
import subprocess
from pathlib import Path

def check_environment():
    """Check virtual environment and dependencies"""
    print("🔍 Checking Environment...")
    
    # Check if in virtual environment
    if sys.prefix == sys.base_prefix:
        print("⚠️  Not in virtual environment")
        return False
    else:
        print("✅ Virtual environment active")
    
    # Check key imports
    try:
        import cv2
        import numpy as np
        import torch
        from ultralytics import YOLO
        print("✅ Key dependencies available")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        return False

def check_demo_files():
    """Check demo files exist"""
    print("📋 Checking Demo Files...")
    
    required_files = [
        "core/real_ptz_demo.py",
        "core/ptz_controller.py", 
        "core/enhanced_visualizer.py",
        "core/camera_calibration.py",
        "core/setup_optimal_position.py"
    ]
    
    all_good = True
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            all_good = False
    
    return all_good

def test_simulation_mode():
    """Test simulation mode briefly"""
    print("🎨 Testing Simulation Mode...")
    
    try:
        # Quick test of simulation mode
        result = subprocess.run([
            sys.executable, "core/real_ptz_demo.py", 
            "--simulation", "--enhanced-viz", "--no-display"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ Simulation mode working")
            return True
        else:
            print(f"❌ Simulation mode failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("✅ Simulation mode started (timeout expected)")
        return True
    except Exception as e:
        print(f"❌ Simulation test error: {e}")
        return False

def main():
    """Main validation"""
    print("✅ AutoZoom Demo Validation")
    print("=" * 40)
    
    tests = [
        ("Environment", check_environment),
        ("Demo Files", check_demo_files),
        ("Simulation Mode", test_simulation_mode)
    ]
    
    passed = 0
    for test_name, test_func in tests:
        if test_func():
            passed += 1
        print()
    
    print("=" * 40)
    print(f"📊 Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 Demo validation successful!")
        print("🚀 Ready to run: ./run_best_demo.sh")
    else:
        print("⚠️  Some issues found. Check above output.")
    
    return 0 if passed == len(tests) else 1

if __name__ == "__main__":
    sys.exit(main())