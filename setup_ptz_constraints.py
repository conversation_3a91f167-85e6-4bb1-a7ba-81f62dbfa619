#!/usr/bin/env python3
"""
🔧 PTZ Camera Constraints Setup
==============================

Interactive setup for PTZ camera constraints in wall-blocked environments.
Helps configure the constrained_environment.json file for your specific setup.

Built with love for worker protection and safety! 💙
"""

import json
import sys
from pathlib import Path

def main():
    """Interactive setup for PTZ constraints"""
    
    print("🔧 PTZ CAMERA CONSTRAINTS SETUP")
    print("=" * 40)
    print("This tool helps you configure camera constraints")
    print("for environments where the camera is blocked by walls.")
    print("=" * 40)
    
    print("\n📋 Current situation:")
    print("• Camera is positioned between two walls")
    print("• Camera can see open space downstairs")
    print("• Panning left/right hits walls immediately")
    print("• Need to restrict to zoom-only operation")
    
    # Ask for confirmation
    setup_zoom_only = input("\n🔒 Enable zoom-only mode? (y/n): ").strip().lower()
    
    if setup_zoom_only != 'y':
        print("❌ Zoom-only mode not enabled. Exiting.")
        return
    
    # Get viewing angle range
    print("\n📐 Camera viewing angle setup:")
    print("The camera can see clearly in a narrow corridor downstairs.")
    print("We need to define the safe viewing range.")
    
    try:
        pan_left = float(input("Left boundary (degrees, negative): ") or "-30")
        pan_right = float(input("Right boundary (degrees, positive): ") or "30")
        tilt_down = float(input("Downward tilt limit (degrees, negative): ") or "-20")
        tilt_up = float(input("Upward tilt limit (degrees, positive): ") or "40")
    except ValueError:
        print("❌ Invalid input. Using defaults.")
        pan_left, pan_right = -30, 30
        tilt_down, tilt_up = -20, 40
    
    # Get zoom settings
    print("\n🔍 Zoom settings:")
    try:
        max_zoom = float(input("Maximum zoom level (1.0-10.0): ") or "8.0")
        min_zoom = float(input("Minimum zoom level: ") or "1.0")
    except ValueError:
        print("❌ Invalid input. Using defaults.")
        max_zoom, min_zoom = 8.0, 1.0
    
    # Create configuration
    config = {
        "blocked_zones": [
            {
                "pan": [-180, pan_left],
                "tilt": [-90, 90],
                "description": "Left wall completely blocked"
            },
            {
                "pan": [pan_right, 180],
                "tilt": [-90, 90],
                "description": "Right wall completely blocked"
            }
        ],
        "safe_zones": [
            {
                "pan": [pan_left, pan_right],
                "tilt": [tilt_down, tilt_up],
                "description": "Open downstairs viewing corridor"
            }
        ],
        "optimal_position": {
            "pan": 0.0,
            "tilt": (tilt_down + tilt_up) / 2,
            "zoom": min_zoom,
            "description": "Center position for downstairs visibility"
        },
        "max_zoom_level": max_zoom,
        "min_zoom_level": min_zoom,
        "zoom_increment": 0.5,
        "max_zoom_attempts": 5,
        "return_to_optimal_timeout": 15.0,
        "zoom_only_mode": True,
        "movement_restrictions": {
            "allow_pan": False,
            "allow_tilt": False,
            "allow_zoom": True,
            "reason": "Camera physically constrained between walls - only zoom operations safe"
        },
        "emergency_return_enabled": True,
        "emergency_return_position": {
            "pan": 0.0,
            "tilt": (tilt_down + tilt_up) / 2,
            "zoom": min_zoom
        }
    }
    
    # Save configuration
    config_path = Path("constrained_environment.json")
    
    print(f"\n💾 Configuration summary:")
    print(f"• Zoom-only mode: ENABLED")
    print(f"• Safe viewing range: {pan_left}° to {pan_right}° (pan)")
    print(f"• Safe tilt range: {tilt_down}° to {tilt_up}° (tilt)")
    print(f"• Zoom range: {min_zoom}x to {max_zoom}x")
    print(f"• Pan/Tilt movements: DISABLED")
    
    save_config = input(f"\n💾 Save configuration to {config_path}? (y/n): ").strip().lower()
    
    if save_config == 'y':
        try:
            with open(config_path, 'w') as f:
                json.dump(config, f, indent=2)
            print(f"✅ Configuration saved to {config_path}")
            
            print("\n🚀 Next steps:")
            print("1. Test your PTZ camera with: python core/real_ptz_demo.py")
            print("2. The camera will now only zoom, no pan/tilt movements")
            print("3. Use 'r' key to return to optimal position")
            print("4. Use '1-9' keys for manual zoom levels")
            
        except Exception as e:
            print(f"❌ Failed to save configuration: {e}")
    else:
        print("❌ Configuration not saved.")

if __name__ == "__main__":
    main()
