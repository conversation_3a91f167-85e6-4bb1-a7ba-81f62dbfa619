#!/usr/bin/env python3
"""
🎯 Instant Advanced Demo
=======================

Shows ALL the advanced features immediately without complex setup:
- Track IDs with special bboxes
- Face detection and zoom-on-face
- Thresholds and mathematical framework
- Real-time data overlays

Built with love for worker protection and safety! 💙
"""

import cv2
import numpy as np
import time
import sys
import os
from typing import List, Dict, Optional, Tuple
import argparse
import logging

# Add paths
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'autozoom_consolidated_demo'))
sys.path.append(os.path.dirname(__file__))

# Import demo components
from advanced_demo_frontend import AdvancedDemoFrontend, AdvancedTrack

class InstantAdvancedDemo:
    """
    🎯 Instant Advanced Demo
    
    Shows all advanced features immediately with mock data if needed
    """
    
    def __init__(self, use_camera: bool = False):
        """Initialize instant demo"""
        
        print("🎯 Starting Instant Advanced Demo...")
        
        self.use_camera = use_camera
        self.frontend = AdvancedDemoFrontend()
        
        # Mock data for demonstration
        self.mock_tracks = self._create_mock_tracks()
        self.frame_count = 0
        self.start_time = time.time()
        
        # Camera/video setup
        self.video_source = None
        self._setup_video_source()
        
        print("✅ Instant Advanced Demo ready!")
    
    def _setup_video_source(self):
        """Setup video source"""
        
        if self.use_camera:
            # Try to connect to PTZ camera ONLY
            try:
                from ptz_camera_interface import create_ptz_camera
                camera = create_ptz_camera()
                if camera.connected:
                    self.video_source = camera.get_video_stream()
                    print("📹 Using real PTZ camera")
                else:
                    print("⚠️ PTZ camera not available, using static demo")
                    self.video_source = None
            except:
                print("⚠️ Camera not available, using static demo")
                self.video_source = None
        else:
            # Try to find a test video
            test_videos = [
                "autozoom_consolidated_demo/videos/test_input.mp4",
                "autozoom_consolidated_demo/videos/test7.mp4", 
                "videos/test_input.mp4"
            ]
            
            for video_path in test_videos:
                if os.path.exists(video_path):
                    self.video_source = cv2.VideoCapture(video_path)
                    print(f"📹 Using video: {video_path}")
                    break
            
            if not self.video_source:
                print("📹 No video found, using static demo frame")
                self.video_source = None
    
    def _create_mock_tracks(self) -> List[AdvancedTrack]:
        """Create mock tracks for demonstration"""
        
        return [
            AdvancedTrack(
                track_id=1,
                bbox=(100, 100, 200, 300),
                confidence=0.85,
                energy=1247.5,
                age=45,
                face_bbox=(120, 120, 180, 180),
                face_confidence=0.78,
                helmet_detected=True,
                helmet_confidence=0.82,
                velocity=(2.3, -1.1),
                zoom_priority=0.65
            ),
            AdvancedTrack(
                track_id=2,
                bbox=(300, 150, 400, 350),
                confidence=0.42,  # Low confidence - zoom target
                energy=856.2,
                age=23,
                face_bbox=(320, 170, 380, 230),
                face_confidence=0.35,  # Low face confidence
                helmet_detected=False,
                helmet_confidence=0.25,
                velocity=(-1.5, 0.8),
                zoom_priority=0.85  # High priority for zoom
            ),
            AdvancedTrack(
                track_id=3,
                bbox=(500, 200, 580, 380),
                confidence=0.71,
                energy=1456.8,
                age=67,
                face_bbox=(510, 220, 570, 280),
                face_confidence=0.89,
                helmet_detected=True,
                helmet_confidence=0.91,
                velocity=(0.2, -2.1),
                zoom_priority=0.25
            )
        ]
    
    def _animate_tracks(self):
        """Animate tracks for dynamic demo"""
        
        # Animate track positions
        for track in self.mock_tracks:
            # Move tracks based on velocity
            vx, vy = track.velocity
            x1, y1, x2, y2 = track.bbox
            
            # Update position
            x1 += vx
            y1 += vy
            x2 += vx
            y2 += vy
            
            # Keep in bounds (640x480 assumed)
            if x1 < 0 or x2 > 640:
                track.velocity = (-vx, vy)
            if y1 < 0 or y2 > 480:
                track.velocity = (vx, -vy)
            
            track.bbox = (max(0, x1), max(0, y1), min(640, x2), min(480, y2))
            
            # Animate confidence
            track.confidence += np.sin(time.time() + track.track_id) * 0.01
            track.confidence = np.clip(track.confidence, 0.2, 0.95)
            
            # Animate energy
            track.energy += np.cos(time.time() * 2 + track.track_id) * 50
            track.energy = max(200, track.energy)
            
            # Update face bbox relative to main bbox
            if track.face_bbox:
                fx1, fy1, fx2, fy2 = track.face_bbox
                offset_x = x1 - (track.bbox[0] if hasattr(track, '_prev_bbox') else x1)
                offset_y = y1 - (track.bbox[1] if hasattr(track, '_prev_bbox') else y1)
                track.face_bbox = (fx1 + offset_x, fy1 + offset_y, fx2 + offset_x, fy2 + offset_y)
    
    def run_demo(self):
        """Run the instant advanced demo"""
        
        print("\n🎯 Instant Advanced Demo Started!")
        print("=" * 50)
        print("Features Demonstrated:")
        print("✅ Track IDs with special bounding boxes")
        print("✅ Face detection and zoom-on-face targeting")
        print("✅ Confidence bars and threshold indicators")
        print("✅ Mathematical framework overlay (E ∝ zoom²)")
        print("✅ Real-time performance metrics")
        print("✅ System status and zoom operations")
        print("=" * 50)
        print("Controls:")
        print("  q - Quit demo")
        print("  f - Toggle face detection")
        print("  t - Toggle thresholds")
        print("  m - Toggle mathematical overlay")
        print("  SPACE - Reset tracks")
        print("=" * 50)
        
        try:
            while True:
                # Read frame
                if self.video_source:
                    ret, frame = self.video_source.read()
                    if not ret:
                        # Reset video or create blank frame
                        if hasattr(self.video_source, 'set'):
                            self.video_source.set(cv2.CAP_PROP_POS_FRAMES, 0)
                            continue
                        else:
                            frame = np.zeros((480, 640, 3), dtype=np.uint8)
                else:
                    # Create blank frame
                    frame = np.zeros((480, 640, 3), dtype=np.uint8)
                
                # Resize frame if needed
                if frame.shape[:2] != (480, 640):
                    frame = cv2.resize(frame, (640, 480))
                
                # Animate mock tracks
                self._animate_tracks()
                
                # Create zoom info (simulate zoom on track 2 with low confidence)
                zoom_info = {
                    'is_zooming': self.mock_tracks[1].confidence < 0.5,
                    'zoom_target': self.mock_tracks[1] if self.mock_tracks[1].confidence < 0.5 else None,
                    'zoom_level': 2.5 if self.mock_tracks[1].confidence < 0.5 else 1.0,
                    'tracks': self.mock_tracks
                }
                
                # Process frame with advanced frontend
                demo_frame = self.frontend.process_frame_for_demo(
                    frame, self.mock_tracks, [], zoom_info
                )
                
                # Add demo title
                cv2.putText(demo_frame, "ADVANCED AUTOZOOM DEMO - ALL FEATURES", 
                           (10, demo_frame.shape[0] - 10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
                
                # Display frame
                cv2.imshow('🎯 Advanced AutoZoom Demo - ALL FEATURES', demo_frame)
                
                # Handle keyboard input
                key = cv2.waitKey(1) & 0xFF
                
                if key == ord('q'):
                    print("🛑 Demo stopped")
                    break
                elif key == ord('f'):
                    self.frontend.toggle_feature('face_detection')
                    print("🔄 Toggled face detection")
                elif key == ord('t'):
                    self.frontend.toggle_feature('thresholds')
                    print("🔄 Toggled thresholds")
                elif key == ord('m'):
                    self.frontend.toggle_feature('mathematical')
                    print("🔄 Toggled mathematical overlay")
                elif key == ord(' '):
                    self.mock_tracks = self._create_mock_tracks()
                    print("🔄 Reset tracks")
                
                self.frame_count += 1
                
                # Show FPS every 30 frames
                if self.frame_count % 30 == 0:
                    elapsed = time.time() - self.start_time
                    fps = self.frame_count / elapsed
                    print(f"📊 Demo running at {fps:.1f} FPS")
                    
        except KeyboardInterrupt:
            print("\n🛑 Demo interrupted")
        
        finally:
            # Cleanup
            if self.video_source:
                self.video_source.release()
            cv2.destroyAllWindows()
            print("✅ Demo completed")

def main():
    """Main demo function"""
    
    parser = argparse.ArgumentParser(description="Instant Advanced Demo")
    parser.add_argument('--camera', action='store_true',
                       help='Use camera instead of video/mock')
    parser.add_argument('--log-level', default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='Logging level')
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(level=getattr(logging, args.log_level))
    
    # Create and run demo
    demo = InstantAdvancedDemo(use_camera=args.camera)
    demo.run_demo()

if __name__ == "__main__":
    main()