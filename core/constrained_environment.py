#!/usr/bin/env python3
"""
🎯 Constrained Environment Handler
==================================

Handles camera operations in constrained environments with blocked areas.
Prevents zoom operations that would result in blocked views.

Built with love for worker protection and safety! 💙
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import numpy as np

logger = logging.getLogger(__name__)

class ConstrainedEnvironmentHandler:
    """
    🎯 Constrained Environment Handler
    
    Manages camera operations in environments with physical constraints,
    blocked areas, and limited visibility zones.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize constrained environment handler
        
        Args:
            config_path: Path to environment configuration file
        """
        
        self.config_path = config_path or "constrained_environment.json"
        self.config = self._load_default_config()
        
        # Load configuration if file exists
        if Path(self.config_path).exists():
            try:
                with open(self.config_path, 'r') as f:
                    saved_config = json.load(f)
                    self.config.update(saved_config)
                logger.info(f"✅ Loaded constrained environment config from {self.config_path}")
            except Exception as e:
                logger.warning(f"⚠️ Failed to load config: {e}, using defaults")
        
        # Initialize constraint zones
        self.blocked_zones = self.config.get('blocked_zones', [])
        self.safe_zones = self.config.get('safe_zones', [])
        self.optimal_position = self.config.get('optimal_position', {})
        self.max_zoom_level = self.config.get('max_zoom_level', 5.0)
        self.min_zoom_level = self.config.get('min_zoom_level', 1.0)
        
        logger.info(f"📐 Constrained environment initialized:")
        logger.info(f"   Blocked zones: {len(self.blocked_zones)}")
        logger.info(f"   Safe zones: {len(self.safe_zones)}")
        logger.info(f"   Zoom range: {self.min_zoom_level}x - {self.max_zoom_level}x")
    
    def _load_default_config(self) -> Dict:
        """Load default configuration for constrained environment"""
        
        return {
            "blocked_zones": [
                # Example blocked zones - adjust based on your environment
                {"pan": (-30, -10), "tilt": (-10, 10), "description": "Wall blocked area"},
                {"pan": (40, 60), "tilt": (-5, 15), "description": "Obstruction zone"}
            ],
            "safe_zones": [
                # Example safe zones - areas with good visibility
                {"pan": (-10, 40), "tilt": (-5, 20), "description": "Main work area"},
                {"pan": (10, 30), "tilt": (5, 25), "description": "Secondary viewing area"}
            ],
            "optimal_position": {
                "pan": 15.0,
                "tilt": 10.0,
                "zoom": 1.0,
                "description": "Best overall visibility position"
            },
            "max_zoom_level": 4.0,  # Conservative max zoom to prevent getting stuck
            "min_zoom_level": 1.0,
            "zoom_increment": 0.5,
            "max_zoom_attempts": 3,
            "return_to_optimal_timeout": 10.0
        }
    
    def save_config(self):
        """Save current configuration to file"""
        
        try:
            with open(self.config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
            logger.info(f"✅ Saved constrained environment config to {self.config_path}")
        except Exception as e:
            logger.error(f"❌ Failed to save config: {e}")
    
    def is_position_blocked(self, pan: float, tilt: float, zoom: float = 1.0) -> bool:
        """
        Check if a camera position would result in blocked view
        
        Args:
            pan: Pan angle in degrees
            tilt: Tilt angle in degrees
            zoom: Zoom level
            
        Returns:
            True if position is blocked, False if safe
        """
        
        # Check blocked zones
        for zone in self.blocked_zones:
            pan_range = zone.get('pan', (-180, 180))
            tilt_range = zone.get('tilt', (-90, 90))
            
            if (pan_range[0] <= pan <= pan_range[1] and 
                tilt_range[0] <= tilt <= tilt_range[1]):
                logger.debug(f"🚫 Position blocked: P:{pan:.1f}° T:{tilt:.1f}° - {zone.get('description', 'Unknown')}")
                return True
        
        # Check zoom limits
        if zoom > self.max_zoom_level:
            logger.debug(f"🚫 Zoom level {zoom:.1f}x exceeds maximum {self.max_zoom_level}x")
            return True
        
        if zoom < self.min_zoom_level:
            logger.debug(f"🚫 Zoom level {zoom:.1f}x below minimum {self.min_zoom_level}x")
            return True
        
        return False
    
    def is_position_safe(self, pan: float, tilt: float, zoom: float = 1.0) -> bool:
        """
        Check if a camera position is in a safe zone
        
        Args:
            pan: Pan angle in degrees
            tilt: Tilt angle in degrees
            zoom: Zoom level
            
        Returns:
            True if position is safe, False otherwise
        """
        
        # First check if not blocked
        if self.is_position_blocked(pan, tilt, zoom):
            return False
        
        # Check if in safe zones
        for zone in self.safe_zones:
            pan_range = zone.get('pan', (-180, 180))
            tilt_range = zone.get('tilt', (-90, 90))
            
            if (pan_range[0] <= pan <= pan_range[1] and 
                tilt_range[0] <= tilt <= tilt_range[1]):
                logger.debug(f"✅ Position safe: P:{pan:.1f}° T:{tilt:.1f}° - {zone.get('description', 'Unknown')}")
                return True
        
        # If no safe zones defined, consider anything not blocked as safe
        if not self.safe_zones:
            return True
        
        logger.debug(f"⚠️ Position not in safe zone: P:{pan:.1f}° T:{tilt:.1f}°")
        return False
    
    def get_safe_zoom_level(self, pan: float, tilt: float, requested_zoom: float) -> float:
        """
        Get maximum safe zoom level for a given position
        
        Args:
            pan: Pan angle in degrees
            tilt: Tilt angle in degrees
            requested_zoom: Requested zoom level
            
        Returns:
            Safe zoom level (may be lower than requested)
        """
        
        # Start with requested zoom and work down
        test_zoom = min(requested_zoom, self.max_zoom_level)
        
        while test_zoom >= self.min_zoom_level:
            if not self.is_position_blocked(pan, tilt, test_zoom):
                logger.debug(f"✅ Safe zoom level: {test_zoom:.1f}x at P:{pan:.1f}° T:{tilt:.1f}°")
                return test_zoom
            
            test_zoom -= self.config.get('zoom_increment', 0.5)
        
        logger.warning(f"⚠️ No safe zoom found, returning minimum: {self.min_zoom_level}x")
        return self.min_zoom_level
    
    def get_optimal_position(self) -> Tuple[float, float, float]:
        """
        Get optimal camera position for best visibility
        
        Returns:
            Tuple of (pan, tilt, zoom) for optimal position
        """
        
        opt_pos = self.optimal_position
        return (
            opt_pos.get('pan', 0.0),
            opt_pos.get('tilt', 0.0),
            opt_pos.get('zoom', 1.0)
        )
    
    def find_alternative_position(self, target_pan: float, target_tilt: float, 
                                 target_zoom: float) -> Optional[Tuple[float, float, float]]:
        """
        Find alternative safe position close to target
        
        Args:
            target_pan: Target pan angle
            target_tilt: Target tilt angle
            target_zoom: Target zoom level
            
        Returns:
            Alternative safe position as (pan, tilt, zoom) or None
        """
        
        # Try positions around the target
        search_radius = 5.0  # degrees
        search_step = 2.0    # degrees
        
        for pan_offset in np.arange(-search_radius, search_radius + 1, search_step):
            for tilt_offset in np.arange(-search_radius, search_radius + 1, search_step):
                alt_pan = target_pan + pan_offset
                alt_tilt = target_tilt + tilt_offset
                alt_zoom = self.get_safe_zoom_level(alt_pan, alt_tilt, target_zoom)
                
                if self.is_position_safe(alt_pan, alt_tilt, alt_zoom):
                    logger.info(f"🔍 Found alternative position: P:{alt_pan:.1f}° T:{alt_tilt:.1f}° Z:{alt_zoom:.1f}x")
                    return (alt_pan, alt_tilt, alt_zoom)
        
        logger.warning("⚠️ No safe alternative position found")
        return None
    
    def validate_zoom_operation(self, current_pan: float, current_tilt: float, 
                               target_pan: float, target_tilt: float, 
                               zoom_level: float) -> Dict:
        """
        Validate a zoom operation before execution
        
        Args:
            current_pan: Current pan position
            current_tilt: Current tilt position
            target_pan: Target pan position
            target_tilt: Target tilt position
            zoom_level: Requested zoom level
            
        Returns:
            Validation result with recommendations
        """
        
        result = {
            'is_safe': False,
            'recommended_pan': target_pan,
            'recommended_tilt': target_tilt,
            'recommended_zoom': zoom_level,
            'reason': '',
            'alternative_available': False
        }
        
        # Check if target position is blocked
        if self.is_position_blocked(target_pan, target_tilt, zoom_level):
            result['reason'] = 'Target position blocked'
            
            # Try to find alternative
            alternative = self.find_alternative_position(target_pan, target_tilt, zoom_level)
            if alternative:
                result['alternative_available'] = True
                result['recommended_pan'] = alternative[0]
                result['recommended_tilt'] = alternative[1]
                result['recommended_zoom'] = alternative[2]
                result['reason'] = 'Using alternative safe position'
                result['is_safe'] = True
            else:
                # Fall back to optimal position
                opt_pan, opt_tilt, opt_zoom = self.get_optimal_position()
                result['recommended_pan'] = opt_pan
                result['recommended_tilt'] = opt_tilt
                result['recommended_zoom'] = opt_zoom
                result['reason'] = 'Falling back to optimal position'
        else:
            # Position is safe
            result['is_safe'] = True
            result['recommended_zoom'] = self.get_safe_zoom_level(target_pan, target_tilt, zoom_level)
            result['reason'] = 'Position validated as safe'
        
        return result
    
    def add_blocked_zone(self, pan_range: Tuple[float, float], 
                        tilt_range: Tuple[float, float], 
                        description: str = ""):
        """Add a new blocked zone"""
        
        new_zone = {
            'pan': pan_range,
            'tilt': tilt_range,
            'description': description
        }
        
        self.blocked_zones.append(new_zone)
        self.config['blocked_zones'] = self.blocked_zones
        
        logger.info(f"➕ Added blocked zone: {description}")
    
    def add_safe_zone(self, pan_range: Tuple[float, float], 
                     tilt_range: Tuple[float, float], 
                     description: str = ""):
        """Add a new safe zone"""
        
        new_zone = {
            'pan': pan_range,
            'tilt': tilt_range,
            'description': description
        }
        
        self.safe_zones.append(new_zone)
        self.config['safe_zones'] = self.safe_zones
        
        logger.info(f"➕ Added safe zone: {description}")
    
    def set_optimal_position(self, pan: float, tilt: float, zoom: float = 1.0):
        """Set new optimal position"""
        
        self.optimal_position = {
            'pan': pan,
            'tilt': tilt,
            'zoom': zoom,
            'description': 'User-defined optimal position'
        }
        
        self.config['optimal_position'] = self.optimal_position
        logger.info(f"🎯 Set optimal position: P:{pan:.1f}° T:{tilt:.1f}° Z:{zoom:.1f}x")
    
    def get_status(self) -> Dict:
        """Get current status of constrained environment"""
        
        return {
            'blocked_zones': len(self.blocked_zones),
            'safe_zones': len(self.safe_zones),
            'optimal_position': self.optimal_position,
            'zoom_limits': {
                'min': self.min_zoom_level,
                'max': self.max_zoom_level
            },
            'config_loaded': Path(self.config_path).exists()
        }