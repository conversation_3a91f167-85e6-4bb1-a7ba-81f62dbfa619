#!/usr/bin/env python3
"""
🎯 Mask and Headphone Detector
==============================

Specialized detector for identifying people wearing masks and headphones.
Perfect for focused tracking demonstrations.

Built with love for worker protection and safety! 💙
"""

import cv2
import numpy as np
import logging
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import sys
import os

# Add paths to access modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'autozoom_consolidated_demo'))

from detector import PersonProxyDetector, Detection

logger = logging.getLogger(__name__)

@dataclass
class MaskHeadphoneDetection:
    """Detection result for mask/headphone detection"""
    bbox: Tuple[int, int, int, int]
    confidence: float
    has_mask: bool
    has_headphones: bool
    mask_confidence: float
    headphone_confidence: float
    person_type: str  # "mask_wearer", "headphone_wearer", "both", "neither"

class MaskHeadphoneDetector:
    """
    🎯 Mask and Headphone Detector
    
    Detects people and specifically identifies those wearing masks and headphones.
    Prioritizes these detections for focused tracking demonstrations.
    """
    
    def __init__(self):
        """Initialize the mask and headphone detector"""
        
        logger.info("🎯 Initializing Mask and Headphone Detector...")
        
        # Initialize base person detector
        self.person_detector = PersonProxyDetector()
        
        # Initialize face detection for mask detection
        self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        
        # Detection parameters
        self.mask_detection_params = {
            'min_face_size': (30, 30),
            'scale_factor': 1.1,
            'min_neighbors': 3,
            'mask_color_ranges': [
                # Blue mask range
                ([100, 50, 50], [130, 255, 255]),
                # White/gray mask range  
                ([0, 0, 100], [180, 30, 255]),
                # Black mask range
                ([0, 0, 0], [180, 255, 50])
            ]
        }
        
        self.headphone_detection_params = {
            'circular_detection': True,
            'color_ranges': [
                # Black headphones
                ([0, 0, 0], [180, 255, 50]),
                # White headphones
                ([0, 0, 200], [180, 30, 255])
            ]
        }
        
        # Priority settings
        self.priority_weights = {
            'mask_wearer': 0.9,
            'headphone_wearer': 0.8,
            'both': 1.0,
            'neither': 0.1
        }
        
        logger.info("✅ Mask and Headphone Detector initialized")
    
    def detect(self, frame: np.ndarray) -> List[MaskHeadphoneDetection]:
        """
        Detect people with masks and headphones
        
        Args:
            frame: Input frame
            
        Returns:
            List of mask/headphone detections
        """
        
        # First, detect all people
        person_detections = self.person_detector.detect(frame)
        
        mask_headphone_detections = []
        
        for detection in person_detections:
            # Extract person region
            x1, y1, x2, y2 = detection.bbox
            person_roi = frame[y1:y2, x1:x2]
            
            if person_roi.size == 0:
                continue
            
            # Detect masks and headphones in person region
            has_mask, mask_confidence = self._detect_mask_in_person(person_roi)
            has_headphones, headphone_confidence = self._detect_headphones_in_person(person_roi)
            
            # Determine person type
            if has_mask and has_headphones:
                person_type = "both"
            elif has_mask:
                person_type = "mask_wearer"
            elif has_headphones:
                person_type = "headphone_wearer"
            else:
                person_type = "neither"
            
            # Only include people with masks or headphones (or both)
            if person_type != "neither":
                # Adjust confidence based on mask/headphone detection
                adjusted_confidence = detection.confidence * self.priority_weights[person_type]
                
                mask_headphone_detection = MaskHeadphoneDetection(
                    bbox=detection.bbox,
                    confidence=adjusted_confidence,
                    has_mask=has_mask,
                    has_headphones=has_headphones,
                    mask_confidence=mask_confidence,
                    headphone_confidence=headphone_confidence,
                    person_type=person_type
                )
                
                mask_headphone_detections.append(mask_headphone_detection)
        
        # Sort by confidence (prioritize people with masks/headphones)
        mask_headphone_detections.sort(key=lambda x: x.confidence, reverse=True)
        
        logger.debug(f"🎯 Found {len(mask_headphone_detections)} people with masks/headphones")
        
        return mask_headphone_detections
    
    def _detect_mask_in_person(self, person_roi: np.ndarray) -> Tuple[bool, float]:
        """
        Detect if person is wearing a mask
        
        Args:
            person_roi: Person region of interest
            
        Returns:
            Tuple of (has_mask, confidence)
        """
        
        # Convert to grayscale for face detection
        gray = cv2.cvtColor(person_roi, cv2.COLOR_BGR2GRAY)
        
        # Detect faces
        faces = self.face_cascade.detectMultiScale(
            gray,
            scaleFactor=self.mask_detection_params['scale_factor'],
            minNeighbors=self.mask_detection_params['min_neighbors'],
            minSize=self.mask_detection_params['min_face_size']
        )
        
        if len(faces) == 0:
            return False, 0.0
        
        # Check for mask indicators in face regions
        mask_confidence = 0.0
        
        for (fx, fy, fw, fh) in faces:
            # Focus on lower half of face (where mask would be)
            mask_roi = person_roi[fy + fh//2:fy + fh, fx:fx + fw]
            
            if mask_roi.size == 0:
                continue
            
            # Convert to HSV for better color detection
            hsv = cv2.cvtColor(mask_roi, cv2.COLOR_BGR2HSV)
            
            # Check for mask colors
            mask_pixels = 0
            total_pixels = mask_roi.shape[0] * mask_roi.shape[1]
            
            for lower, upper in self.mask_detection_params['mask_color_ranges']:
                mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
                mask_pixels += np.sum(mask > 0)
            
            # Calculate mask confidence
            if total_pixels > 0:
                mask_ratio = mask_pixels / total_pixels
                mask_confidence = max(mask_confidence, mask_ratio)
        
        # Threshold for mask detection
        has_mask = mask_confidence > 0.15  # 15% of lower face area
        
        return has_mask, mask_confidence
    
    def _detect_headphones_in_person(self, person_roi: np.ndarray) -> Tuple[bool, float]:
        """
        Detect if person is wearing headphones
        
        Args:
            person_roi: Person region of interest
            
        Returns:
            Tuple of (has_headphones, confidence)
        """
        
        # Focus on head area (upper 30% of person)
        head_height = person_roi.shape[0] // 3
        head_roi = person_roi[:head_height, :]
        
        if head_roi.size == 0:
            return False, 0.0
        
        # Convert to HSV for color detection
        hsv = cv2.cvtColor(head_roi, cv2.COLOR_BGR2HSV)
        
        # Check for headphone colors
        headphone_pixels = 0
        total_pixels = head_roi.shape[0] * head_roi.shape[1]
        
        for lower, upper in self.headphone_detection_params['color_ranges']:
            mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
            headphone_pixels += np.sum(mask > 0)
        
        # Calculate headphone confidence
        headphone_confidence = 0.0
        if total_pixels > 0:
            headphone_ratio = headphone_pixels / total_pixels
            headphone_confidence = headphone_ratio
        
        # Look for circular patterns (headphone shapes)
        gray = cv2.cvtColor(head_roi, cv2.COLOR_BGR2GRAY)
        circles = cv2.HoughCircles(
            gray,
            cv2.HOUGH_GRADIENT,
            dp=1,
            minDist=30,
            param1=50,
            param2=30,
            minRadius=10,
            maxRadius=50
        )
        
        # Boost confidence if circular patterns found
        if circles is not None and len(circles[0]) > 0:
            headphone_confidence += 0.3
        
        # Threshold for headphone detection
        has_headphones = headphone_confidence > 0.2  # 20% confidence threshold
        
        return has_headphones, headphone_confidence
    
    def convert_to_standard_detections(self, mask_headphone_detections: List[MaskHeadphoneDetection]) -> List[Detection]:
        """
        Convert mask/headphone detections to standard Detection objects
        
        Args:
            mask_headphone_detections: List of mask/headphone detections
            
        Returns:
            List of standard Detection objects
        """
        
        standard_detections = []
        
        for detection in mask_headphone_detections:
            # Create standard detection
            standard_detection = Detection(
                bbox=detection.bbox,
                confidence=detection.confidence,
                class_name="person"
            )
            
            # Add mask/headphone metadata
            standard_detection.metadata = {
                'has_mask': detection.has_mask,
                'has_headphones': detection.has_headphones,
                'mask_confidence': detection.mask_confidence,
                'headphone_confidence': detection.headphone_confidence,
                'person_type': detection.person_type,
                'priority_weight': self.priority_weights[detection.person_type]
            }
            
            standard_detections.append(standard_detection)
        
        return standard_detections
    
    def get_detection_stats(self, detections: List[MaskHeadphoneDetection]) -> Dict:
        """Get statistics about detections"""
        
        stats = {
            'total_detections': len(detections),
            'mask_wearers': sum(1 for d in detections if d.has_mask),
            'headphone_wearers': sum(1 for d in detections if d.has_headphones),
            'both_mask_and_headphones': sum(1 for d in detections if d.has_mask and d.has_headphones),
            'avg_mask_confidence': np.mean([d.mask_confidence for d in detections if d.has_mask]) if any(d.has_mask for d in detections) else 0.0,
            'avg_headphone_confidence': np.mean([d.headphone_confidence for d in detections if d.has_headphones]) if any(d.has_headphones for d in detections) else 0.0
        }
        
        return stats

if __name__ == "__main__":
    # Test the detector
    logging.basicConfig(level=logging.INFO)
    
    detector = MaskHeadphoneDetector()
    
    # Test with a sample frame
    test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
    detections = detector.detect(test_frame)
    
    stats = detector.get_detection_stats(detections)
    print(f"Detection stats: {stats}")
    
    print("✅ Mask and Headphone Detector test completed")