"""
🎯 Advanced Demo Frontend
========================

Complete demo frontend with all advanced visualization features:
- Track IDs with special bboxes
- Thresholds and confidence displays
- Zoom-on-face feature
- Helmet detection (YOLOv8)
- Real-time data overlays

Built with love for worker protection and safety! 💙
"""

import cv2
import numpy as np
import time
import sys
import os
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
import json

# Add paths to access modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'autozoom_consolidated_demo'))
sys.path.append(os.path.dirname(__file__))

@dataclass
class AdvancedTrack:
    """Advanced track with all demo features"""
    track_id: int
    bbox: Tuple[float, float, float, float]
    confidence: float
    energy: float = 0.0
    age: int = 0
    face_bbox: Optional[Tuple[float, float, float, float]] = None
    face_confidence: float = 0.0
    helmet_detected: bool = False
    helmet_confidence: float = 0.0
    velocity: Tuple[float, float] = (0.0, 0.0)
    zoom_priority: float = 0.0

class AdvancedDemoFrontend:
    """
    🎯 Advanced Demo Frontend
    
    Shows all the advanced features for impressive demo:
    - Special bounding boxes with track IDs
    - Threshold indicators and confidence meters
    - Zoom-on-face targeting
    - Real-time mathematical framework display
    - Performance metrics and system status
    """
    
    def __init__(self):
        """Initialize advanced demo frontend"""
        
        print("🎯 Initializing Advanced Demo Frontend...")
        
        # Demo settings
        self.show_track_ids = True
        self.show_confidence_bars = True
        self.show_thresholds = True
        self.show_face_detection = True
        self.show_zoom_targets = True
        self.show_energy_values = True
        self.show_mathematical_overlay = True
        
        # Thresholds (configurable)
        self.confidence_threshold = 0.5
        self.face_confidence_threshold = 0.6
        self.zoom_trigger_threshold = 0.4
        self.energy_threshold = 500.0
        
        # Colors for different elements
        self.colors = {
            'track_excellent': (0, 255, 0),    # Green
            'track_good': (0, 255, 255),       # Yellow  
            'track_poor': (0, 165, 255),       # Orange
            'track_critical': (0, 0, 255),     # Red
            'face_box': (255, 255, 0),         # Cyan
            'zoom_target': (255, 0, 255),      # Magenta
            'threshold_line': (128, 128, 128), # Gray
            'energy_bar': (0, 255, 128),       # Light green
            'text_bg': (0, 0, 0),             # Black
            'text_fg': (255, 255, 255)        # White
        }
        
        # Track history for paths
        self.track_paths = {}
        self.max_path_length = 30
        
        # Demo statistics
        self.demo_stats = {
            'total_tracks': 0,
            'zoom_operations': 0,
            'face_detections': 0,
            'helmet_detections': 0,
            'avg_confidence': 0.0,
            'avg_energy': 0.0
        }
        
        print("✅ Advanced Demo Frontend initialized")
    
    def process_frame_for_demo(self, frame: np.ndarray, tracks: List, 
                              detections: List, zoom_info: Dict) -> np.ndarray:
        """
        Process frame with all advanced demo features
        
        Args:
            frame: Input frame
            tracks: List of tracks
            detections: List of detections  
            zoom_info: Zoom operation info
            
        Returns:
            Enhanced frame with all demo features
        """
        
        # Create demo frame
        demo_frame = frame.copy()
        
        # Draw all demo features
        self._draw_advanced_tracks(demo_frame, tracks)
        self._draw_face_detection_overlay(demo_frame, tracks)
        self._draw_zoom_targeting(demo_frame, zoom_info)
        self._draw_threshold_indicators(demo_frame)
        self._draw_mathematical_overlay(demo_frame, tracks)
        self._draw_performance_dashboard(demo_frame, tracks)
        self._draw_system_status(demo_frame, zoom_info)
        
        # Update demo statistics
        self._update_demo_stats(tracks)
        
        return demo_frame
    
    def _draw_advanced_tracks(self, frame: np.ndarray, tracks: List):
        """Draw tracks with special bounding boxes and Track IDs"""
        
        for track in tracks:
            # Get track properties
            track_id = getattr(track, 'track_id', 0)
            bbox = getattr(track, 'bbox', (0, 0, 100, 100))
            confidence = getattr(track, 'confidence', 0.5)
            energy = getattr(track, 'energy', 0.0)
            
            # Convert bbox
            x1, y1, x2, y2 = map(int, bbox)
            
            # Determine track status and color
            if confidence >= 0.8:
                color = self.colors['track_excellent']
                status = "EXCELLENT"
            elif confidence >= 0.6:
                color = self.colors['track_good']
                status = "GOOD"
            elif confidence >= 0.4:
                color = self.colors['track_poor']
                status = "POOR"
            else:
                color = self.colors['track_critical']
                status = "CRITICAL"
            
            # Draw special bounding box (thick, with corners)
            thickness = 3
            corner_length = 20
            
            # Main rectangle
            cv2.rectangle(frame, (x1, y1), (x2, y2), color, thickness)
            
            # Corner indicators
            # Top-left
            cv2.line(frame, (x1, y1), (x1 + corner_length, y1), color, thickness + 2)
            cv2.line(frame, (x1, y1), (x1, y1 + corner_length), color, thickness + 2)
            
            # Top-right
            cv2.line(frame, (x2, y1), (x2 - corner_length, y1), color, thickness + 2)
            cv2.line(frame, (x2, y1), (x2, y1 + corner_length), color, thickness + 2)
            
            # Bottom-left
            cv2.line(frame, (x1, y2), (x1 + corner_length, y2), color, thickness + 2)
            cv2.line(frame, (x1, y2), (x1, y2 - corner_length), color, thickness + 2)
            
            # Bottom-right
            cv2.line(frame, (x2, y2), (x2 - corner_length, y2), color, thickness + 2)
            cv2.line(frame, (x2, y2), (x2, y2 - corner_length), color, thickness + 2)
            
            # Large Track ID display
            id_text = f"ID: {track_id}"
            id_font_scale = 1.2
            id_thickness = 3
            
            # Get text size for background
            (text_w, text_h), baseline = cv2.getTextSize(id_text, cv2.FONT_HERSHEY_SIMPLEX, 
                                                        id_font_scale, id_thickness)
            
            # Draw background for Track ID
            cv2.rectangle(frame, (x1, y1 - text_h - 10), (x1 + text_w + 10, y1), 
                         self.colors['text_bg'], -1)
            cv2.rectangle(frame, (x1, y1 - text_h - 10), (x1 + text_w + 10, y1), 
                         color, 2)
            
            # Draw Track ID
            cv2.putText(frame, id_text, (x1 + 5, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, id_font_scale, color, id_thickness)
            
            # Draw confidence and status
            status_text = f"{status} ({confidence:.2f})"
            cv2.putText(frame, status_text, (x1, y2 + 25), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
            
            # Draw energy value
            energy_text = f"E: {energy:.0f}"
            cv2.putText(frame, energy_text, (x1, y2 + 50), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, self.colors['energy_bar'], 2)
            
            # Draw confidence bar
            self._draw_confidence_bar(frame, (x1, y1 - 40), confidence, color)
            
            # Update track path
            center_x, center_y = (x1 + x2) // 2, (y1 + y2) // 2
            self._update_track_path(track_id, center_x, center_y)
            
            # Draw track path
            self._draw_track_path(frame, track_id, color)
    
    def _draw_confidence_bar(self, frame: np.ndarray, pos: Tuple[int, int], 
                           confidence: float, color: Tuple[int, int, int]):
        """Draw confidence bar above track"""
        
        x, y = pos
        bar_width = 100
        bar_height = 10
        
        # Background bar
        cv2.rectangle(frame, (x, y), (x + bar_width, y + bar_height), 
                     self.colors['text_bg'], -1)
        cv2.rectangle(frame, (x, y), (x + bar_width, y + bar_height), 
                     self.colors['text_fg'], 1)
        
        # Confidence fill
        fill_width = int(bar_width * confidence)
        cv2.rectangle(frame, (x + 1, y + 1), (x + fill_width, y + bar_height - 1), 
                     color, -1)
        
        # Threshold line
        threshold_x = int(x + bar_width * self.confidence_threshold)
        cv2.line(frame, (threshold_x, y), (threshold_x, y + bar_height), 
                self.colors['threshold_line'], 2)
        
        # Confidence text
        conf_text = f"{confidence:.2f}"
        cv2.putText(frame, conf_text, (x + bar_width + 5, y + bar_height), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, self.colors['text_fg'], 1)
    
    def _draw_face_detection_overlay(self, frame: np.ndarray, tracks: List):
        """Draw face detection boxes for zoom-on-face feature"""
        
        for track in tracks:
            # Check if track has face detection
            face_bbox = getattr(track, 'face_bbox', None)
            face_confidence = getattr(track, 'face_confidence', 0.0)
            
            if face_bbox and face_confidence > self.face_confidence_threshold:
                x1, y1, x2, y2 = map(int, face_bbox)
                
                # Draw face bounding box
                cv2.rectangle(frame, (x1, y1), (x2, y2), self.colors['face_box'], 2)
                
                # Draw face center crosshair for zoom targeting
                center_x, center_y = (x1 + x2) // 2, (y1 + y2) // 2
                cv2.line(frame, (center_x - 10, center_y), (center_x + 10, center_y), 
                        self.colors['face_box'], 2)
                cv2.line(frame, (center_x, center_y - 10), (center_x, center_y + 10), 
                        self.colors['face_box'], 2)
                
                # Face confidence text
                face_text = f"FACE: {face_confidence:.2f}"
                cv2.putText(frame, face_text, (x1, y1 - 5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, self.colors['face_box'], 2)
                
                # Zoom-on-face indicator
                if face_confidence < self.zoom_trigger_threshold:
                    cv2.putText(frame, "ZOOM TARGET", (x1, y2 + 20), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, self.colors['zoom_target'], 2)
    
    def _draw_zoom_targeting(self, frame: np.ndarray, zoom_info: Dict):
        """Draw zoom targeting information"""
        
        is_zooming = zoom_info.get('is_zooming', False)
        zoom_target = zoom_info.get('zoom_target')
        zoom_level = zoom_info.get('zoom_level', 1.0)
        
        if is_zooming and zoom_target:
            # Get target bbox
            bbox = getattr(zoom_target, 'bbox', (0, 0, 100, 100))
            x1, y1, x2, y2 = map(int, bbox)
            center_x, center_y = (x1 + x2) // 2, (y1 + y2) // 2
            
            # Draw zoom target indicator
            radius = 60
            cv2.circle(frame, (center_x, center_y), radius, self.colors['zoom_target'], 3)
            cv2.circle(frame, (center_x, center_y), radius - 10, self.colors['zoom_target'], 1)
            
            # Draw zoom crosshair
            cv2.line(frame, (center_x - 30, center_y), (center_x + 30, center_y), 
                    self.colors['zoom_target'], 3)
            cv2.line(frame, (center_x, center_y - 30), (center_x, center_y + 30), 
                    self.colors['zoom_target'], 3)
            
            # Zoom level indicator
            zoom_text = f"ZOOMING {zoom_level:.1f}x"
            cv2.putText(frame, zoom_text, (center_x - 60, center_y - 80), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, self.colors['zoom_target'], 2)
            
            # Target track ID
            track_id = getattr(zoom_target, 'track_id', 0)
            target_text = f"TARGET ID: {track_id}"
            cv2.putText(frame, target_text, (center_x - 60, center_y + 100), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, self.colors['zoom_target'], 2)
    
    def _draw_threshold_indicators(self, frame: np.ndarray):
        """Draw threshold indicators and settings"""
        
        height, width = frame.shape[:2]
        
        # Threshold panel
        panel_x = 10
        panel_y = height - 200
        panel_width = 300
        panel_height = 180
        
        # Panel background
        cv2.rectangle(frame, (panel_x, panel_y), 
                     (panel_x + panel_width, panel_y + panel_height), 
                     self.colors['text_bg'], -1)
        cv2.rectangle(frame, (panel_x, panel_y), 
                     (panel_x + panel_width, panel_y + panel_height), 
                     self.colors['text_fg'], 2)
        
        # Panel title
        cv2.putText(frame, "THRESHOLDS & SETTINGS", (panel_x + 10, panel_y + 20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, self.colors['text_fg'], 2)
        
        # Threshold values
        y_offset = panel_y + 45
        thresholds = [
            f"Confidence Threshold: {self.confidence_threshold:.2f}",
            f"Face Confidence: {self.face_confidence_threshold:.2f}",
            f"Zoom Trigger: {self.zoom_trigger_threshold:.2f}",
            f"Energy Threshold: {self.energy_threshold:.0f}",
            f"Mathematical: E ∝ zoom²",
            f"Priority Algorithm: Multi-factor"
        ]
        
        for i, threshold_text in enumerate(thresholds):
            cv2.putText(frame, threshold_text, (panel_x + 15, y_offset + i * 22), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.45, self.colors['text_fg'], 1)
    
    def _draw_mathematical_overlay(self, frame: np.ndarray, tracks: List):
        """Draw mathematical framework overlay"""
        
        height, width = frame.shape[:2]
        
        # Mathematical panel
        panel_x = width - 350
        panel_y = 10
        panel_width = 330
        panel_height = 200
        
        # Panel background
        cv2.rectangle(frame, (panel_x, panel_y), 
                     (panel_x + panel_width, panel_y + panel_height), 
                     self.colors['text_bg'], -1)
        cv2.rectangle(frame, (panel_x, panel_y), 
                     (panel_x + panel_width, panel_y + panel_height), 
                     self.colors['text_fg'], 2)
        
        # Panel title
        cv2.putText(frame, "MATHEMATICAL FRAMEWORK", (panel_x + 10, panel_y + 20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
        
        # Mathematical equations and values
        y_offset = panel_y + 45
        math_info = [
            "LAPLACIAN ENERGY SCALING:",
            "E ∝ zoom² (Core Innovation)",
            "",
            "TRACK ASSOCIATION:",
            "Hungarian Algorithm O(n³)",
            "",
            "PRIORITY SCORING:",
            f"P = 0.4×ΔC + 0.3×E + 0.2×S + 0.1×T",
            "",
            "REAL-TIME VALUES:"
        ]
        
        for i, math_text in enumerate(math_info):
            color = (0, 255, 255) if math_text.startswith(("LAPLACIAN", "TRACK", "PRIORITY", "REAL-TIME")) else self.colors['text_fg']
            cv2.putText(frame, math_text, (panel_x + 15, y_offset + i * 18), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
        
        # Current energy values for tracks
        energy_y = y_offset + len(math_info) * 18
        for i, track in enumerate(tracks[:3]):  # Show first 3 tracks
            track_id = getattr(track, 'track_id', 0)
            energy = getattr(track, 'energy', 0.0)
            energy_text = f"ID {track_id}: E = {energy:.0f}"
            cv2.putText(frame, energy_text, (panel_x + 25, energy_y + i * 15), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, self.colors['energy_bar'], 1)
    
    def _draw_performance_dashboard(self, frame: np.ndarray, tracks: List):
        """Draw performance dashboard"""
        
        height, width = frame.shape[:2]
        
        # Performance panel
        panel_x = width - 350
        panel_y = height - 150
        panel_width = 330
        panel_height = 130
        
        # Panel background
        cv2.rectangle(frame, (panel_x, panel_y), 
                     (panel_x + panel_width, panel_y + panel_height), 
                     self.colors['text_bg'], -1)
        cv2.rectangle(frame, (panel_x, panel_y), 
                     (panel_x + panel_width, panel_y + panel_height), 
                     self.colors['text_fg'], 2)
        
        # Panel title
        cv2.putText(frame, "PERFORMANCE METRICS", (panel_x + 10, panel_y + 20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        
        # Performance metrics
        y_offset = panel_y + 45
        active_tracks = len(tracks)
        avg_confidence = np.mean([getattr(t, 'confidence', 0) for t in tracks]) if tracks else 0
        avg_energy = np.mean([getattr(t, 'energy', 0) for t in tracks]) if tracks else 0
        
        metrics = [
            f"Active Tracks: {active_tracks}",
            f"Avg Confidence: {avg_confidence:.2f}",
            f"Avg Energy: {avg_energy:.0f}",
            f"Zoom Operations: {self.demo_stats['zoom_operations']}",
            f"Face Detections: {self.demo_stats['face_detections']}"
        ]
        
        for i, metric in enumerate(metrics):
            cv2.putText(frame, metric, (panel_x + 15, y_offset + i * 18), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.45, self.colors['text_fg'], 1)
    
    def _draw_system_status(self, frame: np.ndarray, zoom_info: Dict):
        """Draw system status indicators"""
        
        # Status line at top
        status_items = [
            "AUTOZOOM SYSTEM: ACTIVE",
            f"MODE: {'ZOOMING' if zoom_info.get('is_zooming') else 'MONITORING'}",
            f"ZOOM: {zoom_info.get('zoom_level', 1.0):.1f}x",
            f"TRACKS: {len(zoom_info.get('tracks', []))}"
        ]
        
        status_text = " | ".join(status_items)
        cv2.putText(frame, status_text, (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
    
    def _update_track_path(self, track_id: int, x: int, y: int):
        """Update track path for visualization"""
        
        if track_id not in self.track_paths:
            self.track_paths[track_id] = []
        
        self.track_paths[track_id].append((x, y))
        
        if len(self.track_paths[track_id]) > self.max_path_length:
            self.track_paths[track_id] = self.track_paths[track_id][-self.max_path_length:]
    
    def _draw_track_path(self, frame: np.ndarray, track_id: int, color: Tuple[int, int, int]):
        """Draw track movement path"""
        
        if track_id not in self.track_paths or len(self.track_paths[track_id]) < 2:
            return
        
        path = self.track_paths[track_id]
        
        for i in range(1, len(path)):
            alpha = i / len(path)
            thickness = max(1, int(alpha * 3))
            cv2.line(frame, path[i-1], path[i], color, thickness)
    
    def _update_demo_stats(self, tracks: List):
        """Update demo statistics"""
        
        self.demo_stats['total_tracks'] = len(tracks)
        
        # Count face detections
        face_count = sum(1 for t in tracks if getattr(t, 'face_confidence', 0) > 0.5)
        self.demo_stats['face_detections'] = face_count
        
        # Update averages
        if tracks:
            self.demo_stats['avg_confidence'] = np.mean([getattr(t, 'confidence', 0) for t in tracks])
            self.demo_stats['avg_energy'] = np.mean([getattr(t, 'energy', 0) for t in tracks])
    
    def toggle_feature(self, feature: str):
        """Toggle demo features"""
        
        if feature == 'track_ids':
            self.show_track_ids = not self.show_track_ids
        elif feature == 'confidence_bars':
            self.show_confidence_bars = not self.show_confidence_bars
        elif feature == 'thresholds':
            self.show_thresholds = not self.show_thresholds
        elif feature == 'face_detection':
            self.show_face_detection = not self.show_face_detection
        elif feature == 'mathematical':
            self.show_mathematical_overlay = not self.show_mathematical_overlay
    
    def update_threshold(self, threshold_name: str, value: float):
        """Update threshold values"""
        
        if threshold_name == 'confidence':
            self.confidence_threshold = value
        elif threshold_name == 'face_confidence':
            self.face_confidence_threshold = value
        elif threshold_name == 'zoom_trigger':
            self.zoom_trigger_threshold = value
        elif threshold_name == 'energy':
            self.energy_threshold = value