"""
🎯 Hybrid PTZ Controller
========================

Universal PTZ controller that can work with both real PTZ cameras and simulation.
Seamlessly switches between real camera control and zoom simulation based on availability.

Built with love for worker protection and safety! 💙
"""

import cv2
import numpy as np
import time
import logging
from typing import Dict, Tuple, Optional, Any, Union, List
from dataclasses import dataclass
from enum import Enum
import sys
import os

# Add the paths to access modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'autozoom_consolidated_demo'))
sys.path.append(os.path.dirname(__file__))

from ptz_camera_interface import DahuaPTZCamera, create_ptz_camera
from zoom_simulator import PTZZoomSimulator
from constrained_environment import ConstrainedEnvironmentHandler

logger = logging.getLogger(__name__)

class PTZMode(Enum):
    """PTZ operation modes"""
    SIMULATION = "simulation"
    REAL_CAMERA = "real_camera"
    HYBRID = "hybrid"

@dataclass
class PTZState:
    """Current PTZ state information"""
    mode: PTZMode
    is_zooming: bool = False
    zoom_level: float = 1.0
    zoom_center: Tuple[float, float] = (0.5, 0.5)
    zoom_target_id: Optional[int] = None
    zoom_start_time: Optional[float] = None
    camera_connected: bool = False
    last_error: Optional[str] = None

class HybridPTZController:
    """
    🎯 Hybrid PTZ Controller
    
    Universal controller that can operate in three modes:
    1. SIMULATION: Pure simulation mode (crop + resize)
    2. REAL_CAMERA: Real PTZ camera control
    3. HYBRID: Real camera with simulation fallback
    
    Automatically detects camera availability and switches modes appropriately.
    """
    
    def __init__(self, camera_ip: str = "**************", 
                 camera_username: str = "admin", 
                 camera_password: str = "admin1234",
                 prefer_real_camera: bool = True):
        """
        Initialize hybrid PTZ controller
        
        Args:
            camera_ip: Real camera IP address
            camera_username: Camera username
            camera_password: Camera password
            prefer_real_camera: Whether to prefer real camera over simulation
        """
        
        logger.info("🎯 Initializing Hybrid PTZ Controller...")
        
        # Configuration
        self.camera_ip = camera_ip
        self.camera_username = camera_username
        self.camera_password = camera_password
        self.prefer_real_camera = prefer_real_camera
        
        # Initialize components
        self.real_camera = None
        self.simulator = PTZZoomSimulator()
        self.video_stream = None
        self.constrained_env = ConstrainedEnvironmentHandler()
        
        # Controller state
        self.state = PTZState(mode=PTZMode.SIMULATION)
        self.zoom_operations = []
        self.performance_stats = {
            'total_zoom_ops': 0,
            'successful_zooms': 0,
            'camera_reconnects': 0,
            'mode_switches': 0
        }
        
        # Initialize camera connection
        self._initialize_camera()
        
        logger.info(f"✅ Hybrid PTZ Controller initialized in {self.state.mode.value} mode")
    
    def _initialize_camera(self):
        """Initialize real camera connection"""
        if not self.prefer_real_camera:
            self.state.mode = PTZMode.SIMULATION
            return
        
        try:
            logger.info(f"🔌 Attempting to connect to camera at {self.camera_ip}...")
            self.real_camera = create_ptz_camera(
                self.camera_ip, 
                self.camera_username, 
                self.camera_password
            )
            
            if self.real_camera.connected:
                self.state.mode = PTZMode.REAL_CAMERA
                self.state.camera_connected = True
                logger.info("✅ Real camera connected - using REAL_CAMERA mode")
                
                # Initialize video stream
                self.video_stream = self.real_camera.get_video_stream()
                if self.video_stream:
                    logger.info("📹 Video stream initialized")
                else:
                    logger.warning("⚠️ Video stream not available")
                    
            else:
                logger.warning("⚠️ Real camera connection failed - falling back to SIMULATION mode")
                self.state.mode = PTZMode.SIMULATION
                self.state.camera_connected = False
                self.real_camera = None
                
        except Exception as e:
            logger.error(f"❌ Camera initialization error: {e}")
            self.state.mode = PTZMode.SIMULATION
            self.state.camera_connected = False
            self.state.last_error = str(e)
            self.real_camera = None
    
    def get_video_feed(self) -> Optional[cv2.VideoCapture]:
        """Get video feed source"""
        if self.state.mode == PTZMode.REAL_CAMERA and self.video_stream:
            return self.video_stream
        return None
    
    def apply_zoom(self, frame: np.ndarray, zoom_level: float, 
                   zoom_center: Optional[Tuple[float, float]] = None,
                   zoom_bbox: Optional[Tuple[int, int, int, int]] = None,
                   target_track_id: Optional[int] = None) -> np.ndarray:
        """
        Apply zoom operation (real camera or simulation) with constrained environment validation
        
        Args:
            frame: Input frame
            zoom_level: Zoom magnification
            zoom_center: Zoom center in normalized coordinates
            zoom_bbox: Bounding box to zoom to
            target_track_id: Track ID being zoomed on
            
        Returns:
            Processed frame (zoomed or original)
        """
        
        # Calculate zoom target position
        if zoom_center:
            target_center = zoom_center
        elif zoom_bbox:
            # Calculate center from bbox
            x1, y1, x2, y2 = zoom_bbox
            frame_h, frame_w = frame.shape[:2]
            target_center = (
                (x1 + x2) / 2 / frame_w,
                (y1 + y2) / 2 / frame_h
            )
        else:
            target_center = (0.5, 0.5)  # Default center
        
        # Convert normalized coordinates to approximate pan/tilt angles
        # This is a simplified mapping - you may need to adjust based on your camera setup
        target_pan = (target_center[0] - 0.5) * 60.0  # Approximate pan range
        target_tilt = (0.5 - target_center[1]) * 30.0  # Approximate tilt range
        
        # Validate zoom operation with constrained environment
        if self.state.mode == PTZMode.REAL_CAMERA and zoom_level > 1.0:
            # Get current position (approximate)
            current_pan = 0.0  # You may want to get actual position from camera
            current_tilt = 0.0
            
            validation_result = self.constrained_env.validate_zoom_operation(
                current_pan, current_tilt, target_pan, target_tilt, zoom_level
            )
            
            if not validation_result['is_safe']:
                logger.warning(f"⚠️ Zoom operation blocked: {validation_result['reason']}")
                
                # Use recommended safe position
                safe_pan = validation_result['recommended_pan']
                safe_tilt = validation_result['recommended_tilt']
                safe_zoom = validation_result['recommended_zoom']
                
                logger.info(f"🔄 Using safe alternative: P:{safe_pan:.1f}° T:{safe_tilt:.1f}° Z:{safe_zoom:.1f}x")
                
                # Convert back to normalized coordinates
                target_center = (
                    (safe_pan / 60.0) + 0.5,
                    0.5 - (safe_tilt / 30.0)
                )
                zoom_level = safe_zoom
                
                # If falling back to optimal position, log it
                if validation_result['reason'] == 'Falling back to optimal position':
                    logger.info("🏠 Returning to optimal position due to blocked zoom target")
        
        # Update state
        self.state.is_zooming = zoom_level > 1.0
        self.state.zoom_level = zoom_level
        self.state.zoom_target_id = target_track_id
        self.state.zoom_center = target_center
        
        # Track zoom start time
        if self.state.is_zooming and self.state.zoom_start_time is None:
            self.state.zoom_start_time = time.time()
            self.performance_stats['total_zoom_ops'] += 1
            logger.info(f"🎯 Starting zoom operation (level: {zoom_level:.1f}x)")
        elif not self.state.is_zooming and self.state.zoom_start_time is not None:
            zoom_duration = time.time() - self.state.zoom_start_time
            self.state.zoom_start_time = None
            self.performance_stats['successful_zooms'] += 1
            logger.info(f"✅ Zoom operation completed (duration: {zoom_duration:.1f}s)")
        
        # Execute zoom based on mode
        if self.state.mode == PTZMode.REAL_CAMERA:
            return self._apply_real_camera_zoom(frame, zoom_level, zoom_center, zoom_bbox)
        else:
            return self._apply_simulation_zoom(frame, zoom_level, zoom_center, zoom_bbox)
    
    def _apply_real_camera_zoom(self, frame: np.ndarray, zoom_level: float,
                               zoom_center: Optional[Tuple[float, float]],
                               zoom_bbox: Optional[Tuple[int, int, int, int]]) -> np.ndarray:
        """Apply real camera zoom"""
        
        if not self.real_camera or not self.real_camera.connected:
            logger.warning("⚠️ Real camera not available - switching to simulation")
            self.state.mode = PTZMode.SIMULATION
            self.performance_stats['mode_switches'] += 1
            return self._apply_simulation_zoom(frame, zoom_level, zoom_center, zoom_bbox)
        
        try:
            if zoom_level > 1.0:
                # Calculate zoom region
                if zoom_bbox:
                    # Convert bbox to normalized coordinates
                    x1, y1, x2, y2 = zoom_bbox
                    frame_h, frame_w = frame.shape[:2]
                    normalized_bbox = (
                        x1 / frame_w, y1 / frame_h,
                        x2 / frame_w, y2 / frame_h
                    )
                    
                    # Send zoom command to real camera
                    success = self.real_camera.zoom_to_region(
                        normalized_bbox, 
                        zoom_scale=zoom_level, 
                        speed=70.0
                    )
                    
                    if not success:
                        logger.warning("⚠️ Real camera zoom failed - using simulation overlay")
                        return self._apply_simulation_zoom(frame, zoom_level, zoom_center, zoom_bbox)
                        
                elif zoom_center:
                    # Calculate bbox from center and zoom level
                    center_x, center_y = zoom_center
                    fov_scale = 1.0 / zoom_level
                    
                    bbox_normalized = (
                        center_x - fov_scale/2, center_y - fov_scale/2,
                        center_x + fov_scale/2, center_y + fov_scale/2
                    )
                    
                    success = self.real_camera.zoom_to_region(
                        bbox_normalized,
                        zoom_scale=zoom_level,
                        speed=70.0
                    )
                    
                    if not success:
                        logger.warning("⚠️ Real camera zoom failed - using simulation overlay")
                        return self._apply_simulation_zoom(frame, zoom_level, zoom_center, zoom_bbox)
                        
            else:
                # Return to home position
                success = self.real_camera.return_to_home(speed=70.0)
                if not success:
                    logger.warning("⚠️ Failed to return camera to home position")
            
            # For real camera, we return the frame as-is since the camera is doing the zoom
            return frame
            
        except Exception as e:
            logger.error(f"❌ Real camera zoom error: {e}")
            self.state.last_error = str(e)
            
            # Fallback to simulation
            return self._apply_simulation_zoom(frame, zoom_level, zoom_center, zoom_bbox)
    
    def _apply_simulation_zoom(self, frame: np.ndarray, zoom_level: float,
                              zoom_center: Optional[Tuple[float, float]],
                              zoom_bbox: Optional[Tuple[int, int, int, int]]) -> np.ndarray:
        """Apply simulation zoom"""
        
        return self.simulator.apply_zoom(
            frame=frame,
            zoom_level=zoom_level,
            zoom_center=zoom_center,
            zoom_bbox=zoom_bbox
        )
    
    def reset_zoom(self):
        """Reset zoom to normal view and ALWAYS return to optimal position"""
        
        logger.info("🔄 Resetting zoom and returning to optimal position")
        
        self.state.is_zooming = False
        self.state.zoom_level = 1.0
        self.state.zoom_center = (0.5, 0.5)
        self.state.zoom_target_id = None
        self.state.zoom_start_time = None
        
        # ALWAYS return to optimal position for best visibility
        if self.state.mode == PTZMode.REAL_CAMERA and self.real_camera:
            try:
                # Return to home position (optimal position for best visibility)
                success = self.real_camera.return_to_home(speed=80.0)
                if success:
                    logger.info("✅ Camera returned to optimal position for best visibility")
                else:
                    logger.warning("⚠️ Failed to return to optimal position - may have limited visibility")
            except Exception as e:
                logger.error(f"❌ Failed to return real camera to optimal position: {e}")
        
        self.simulator.reset_zoom()
    
    def set_home_position(self) -> bool:
        """Set current position as home position"""
        
        if self.state.mode == PTZMode.REAL_CAMERA and self.real_camera:
            try:
                success = self.real_camera.set_home_position()
                if success:
                    logger.info("🏠 Home position set on real camera")
                    return True
                else:
                    logger.warning("⚠️ Failed to set home position on real camera")
                    return False
            except Exception as e:
                logger.error(f"❌ Error setting home position: {e}")
                return False
        else:
            logger.info("🏠 Home position set (simulation mode)")
            return True
    
    def get_zoom_info(self) -> Dict[str, Any]:
        """Get current zoom information"""
        
        info = {
            'mode': self.state.mode.value,
            'is_zooming': self.state.is_zooming,
            'zoom_level': self.state.zoom_level,
            'zoom_center': self.state.zoom_center,
            'zoom_target_id': self.state.zoom_target_id,
            'camera_connected': self.state.camera_connected,
            'last_error': self.state.last_error
        }
        
        # Add mode-specific information
        if self.state.mode == PTZMode.REAL_CAMERA and self.real_camera:
            camera_status = self.real_camera.get_status()
            info.update({
                'camera_status': camera_status,
                'rtsp_url': camera_status.get('rtsp_url', ''),
                'camera_position': camera_status.get('current_position', {})
            })
        
        # Add simulation info
        if self.state.mode == PTZMode.SIMULATION or self.state.mode == PTZMode.HYBRID:
            sim_info = self.simulator.get_zoom_info()
            info.update({
                'simulation_info': sim_info
            })
        
        return info
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        
        stats = dict(self.performance_stats)
        
        # Add success rate
        if stats['total_zoom_ops'] > 0:
            stats['success_rate'] = stats['successful_zooms'] / stats['total_zoom_ops']
        else:
            stats['success_rate'] = 0.0
        
        # Add mode-specific stats
        if self.state.mode == PTZMode.SIMULATION:
            sim_stats = self.simulator.get_performance_stats()
            stats.update({
                'simulation_stats': sim_stats
            })
        
        return stats
    
    def reconnect_camera(self) -> bool:
        """Attempt to reconnect to real camera"""
        
        if self.state.mode == PTZMode.REAL_CAMERA:
            logger.info("🔄 Camera already connected")
            return True
        
        logger.info("🔄 Attempting to reconnect to real camera...")
        
        try:
            if self.real_camera:
                self.real_camera.disconnect()
            
            self.real_camera = create_ptz_camera(
                self.camera_ip,
                self.camera_username, 
                self.camera_password
            )
            
            if self.real_camera.connected:
                self.state.mode = PTZMode.REAL_CAMERA
                self.state.camera_connected = True
                self.state.last_error = None
                self.performance_stats['camera_reconnects'] += 1
                
                # Reinitialize video stream
                self.video_stream = self.real_camera.get_video_stream()
                
                logger.info("✅ Camera reconnected successfully")
                return True
            else:
                logger.warning("⚠️ Camera reconnection failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Camera reconnection error: {e}")
            self.state.last_error = str(e)
            return False
    
    def switch_to_simulation(self):
        """Switch to simulation mode"""
        
        if self.state.mode == PTZMode.SIMULATION:
            return
        
        logger.info("🔄 Switching to simulation mode")
        
        self.state.mode = PTZMode.SIMULATION
        self.state.camera_connected = False
        self.performance_stats['mode_switches'] += 1
        
        # Reset zoom state
        self.reset_zoom()
    
    def setup_constrained_environment(self, blocked_zones: List[Dict] = None, 
                                     safe_zones: List[Dict] = None,
                                     optimal_position: Dict = None):
        """
        Setup constrained environment configuration
        
        Args:
            blocked_zones: List of blocked zone definitions
            safe_zones: List of safe zone definitions
            optimal_position: Optimal position for best visibility
        """
        
        if blocked_zones:
            for zone in blocked_zones:
                self.constrained_env.add_blocked_zone(
                    zone['pan_range'], zone['tilt_range'], zone.get('description', '')
                )
        
        if safe_zones:
            for zone in safe_zones:
                self.constrained_env.add_safe_zone(
                    zone['pan_range'], zone['tilt_range'], zone.get('description', '')
                )
        
        if optimal_position:
            self.constrained_env.set_optimal_position(
                optimal_position['pan'], optimal_position['tilt'], 
                optimal_position.get('zoom', 1.0)
            )
        
        # Save configuration
        self.constrained_env.save_config()
        logger.info("✅ Constrained environment configuration saved")
    
    def move_to_optimal_position(self) -> bool:
        """Move camera to optimal position for best visibility"""
        
        logger.info("🏠 Moving to optimal position for best visibility...")
        
        if self.state.mode == PTZMode.REAL_CAMERA and self.real_camera:
            try:
                # Get optimal position from constrained environment
                opt_pan, opt_tilt, opt_zoom = self.constrained_env.get_optimal_position()
                
                # Move camera to optimal position
                success = self.real_camera.move_absolute(
                    pan=opt_pan, tilt=opt_tilt, zoom=opt_zoom, speed=80.0
                )
                
                if success:
                    logger.info(f"✅ Camera moved to optimal position: P:{opt_pan:.1f}° T:{opt_tilt:.1f}° Z:{opt_zoom:.1f}x")
                    return True
                else:
                    logger.warning("⚠️ Failed to move to optimal position")
                    return False
                    
            except Exception as e:
                logger.error(f"❌ Error moving to optimal position: {e}")
                return False
        else:
            logger.info("🏠 Optimal position set (simulation mode)")
            return True
    
    def get_constrained_environment_status(self) -> Dict:
        """Get constrained environment status"""
        return self.constrained_env.get_status()
    
    def cleanup(self):
        """Cleanup resources and ALWAYS return to optimal position"""
        
        logger.info("🧹 Cleaning up PTZ controller and returning to optimal position...")
        
        if self.real_camera:
            try:
                # CRITICAL: Always return to optimal position for best visibility
                logger.info("🏠 Returning camera to optimal position for best visibility...")
                
                # First try to use the constrained environment optimal position
                if self.move_to_optimal_position():
                    logger.info("✅ Camera safely returned to optimal position using constrained environment")
                else:
                    # Fallback to home position
                    success = self.real_camera.return_to_home(speed=100.0)
                    if success:
                        logger.info("✅ Camera safely returned to home position")
                    else:
                        logger.warning("⚠️ Failed to return to optimal position - manual repositioning may be needed")
                
                self.real_camera.disconnect()
            except Exception as e:
                logger.error(f"❌ Error during camera cleanup: {e}")
        
        if self.video_stream:
            try:
                self.video_stream.release()
            except Exception as e:
                logger.error(f"❌ Error releasing video stream: {e}")
        
        logger.info("✅ PTZ controller cleanup completed")

def create_ptz_controller(camera_ip: str = "**************",
                         camera_username: str = "admin",
                         camera_password: str = "admin1234",
                         prefer_real_camera: bool = True) -> HybridPTZController:
    """
    Create hybrid PTZ controller with default settings
    
    Args:
        camera_ip: Camera IP address
        camera_username: Camera username
        camera_password: Camera password
        prefer_real_camera: Whether to prefer real camera over simulation
        
    Returns:
        Hybrid PTZ controller instance
    """
    return HybridPTZController(
        camera_ip=camera_ip,
        camera_username=camera_username,
        camera_password=camera_password,
        prefer_real_camera=prefer_real_camera
    )

if __name__ == "__main__":
    # Test the hybrid PTZ controller
    logging.basicConfig(level=logging.INFO)
    
    print("🎯 Testing Hybrid PTZ Controller")
    
    # Create controller
    controller = create_ptz_controller()
    
    # Print initial status
    zoom_info = controller.get_zoom_info()
    print(f"📊 Initial Status: Mode={zoom_info['mode']}, Connected={zoom_info['camera_connected']}")
    
    # Test video feed
    video_feed = controller.get_video_feed()
    if video_feed:
        print("📹 Video feed available")
        
        # Test zoom operations
        print("🎯 Testing zoom operations...")
        
        # Create a test frame
        test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
        cv2.rectangle(test_frame, (100, 100), (200, 200), (0, 255, 0), 2)
        
        # Apply zoom
        zoomed_frame = controller.apply_zoom(
            test_frame,
            zoom_level=2.0,
            zoom_center=(0.3, 0.3)
        )
        
        print(f"✅ Zoom test completed - frame shape: {zoomed_frame.shape}")
        
        # Reset zoom
        controller.reset_zoom()
        print("✅ Zoom reset completed")
        
        video_feed.release()
    else:
        print("⚠️ No video feed available - testing simulation mode")
        
        # Test simulation mode
        controller.switch_to_simulation()
        
        test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
        cv2.rectangle(test_frame, (100, 100), (200, 200), (0, 255, 0), 2)
        
        zoomed_frame = controller.apply_zoom(
            test_frame,
            zoom_level=2.0,
            zoom_center=(0.3, 0.3)
        )
        
        print(f"✅ Simulation zoom test completed - frame shape: {zoomed_frame.shape}")
    
    # Print performance stats
    stats = controller.get_performance_stats()
    print(f"📊 Performance Stats: {stats}")
    
    # Cleanup
    controller.cleanup()
    print("🏁 Test completed")