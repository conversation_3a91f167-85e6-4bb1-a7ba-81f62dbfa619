"""
🎯 Real PTZ Camera Demo
=======================

AutoZoom demo using real Dahua PTZ camera with your advanced mathematical framework.
Integrates real camera control with your Laplacian energy innovation (E ∝ zoom²).

Built with love for worker protection and safety! 💙
"""

import cv2
import numpy as np
import time
import argparse
import logging
import sys
import os
from pathlib import Path
from typing import Optional

# Add paths to access modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'autozoom_consolidated_demo'))
sys.path.append(os.path.dirname(__file__))

from ptz_controller import create_ptz_controller
from autozoom_advanced import AdvancedAutoZoomController
from camera_calibration import PTZCameraCalibrator
from enhanced_visualizer import EnhancedTrackingVisualizer

# Import demo components
from detector import PersonProxyDetector
from tracker import EnergyBasedTracker
from confidence_monitor import ConfidenceMonitor
from visualizer import AutoZoomVisualizer
from config import config

logger = logging.getLogger(__name__)

class RealPTZAutoZoomDemo:
    """
    🎯 Real PTZ Camera AutoZoom Demo
    
    Complete AutoZoom system using real Dahua PTZ camera.
    Combines your mathematical innovations with real hardware control.
    """
    
    def __init__(self, camera_ip: str = "**************",
                 camera_username: str = "admin",
                 camera_password: str = "admin1234",
                 use_real_camera: bool = True):
        """
        Initialize real PTZ demo
        
        Args:
            camera_ip: PTZ camera IP address
            camera_username: Camera username
            camera_password: Camera password
            use_real_camera: Whether to use real camera or simulation
        """
        
        logger.info("🎯 Initializing Real PTZ AutoZoom Demo...")
        
        # Configuration
        self.camera_ip = camera_ip
        self.camera_username = camera_username
        self.camera_password = camera_password
        self.use_real_camera = use_real_camera
        
        # Initialize components
        self.detector = PersonProxyDetector()
        self.tracker = EnergyBasedTracker()
        self.confidence_monitor = ConfidenceMonitor()
        self.autozoom_controller = AdvancedAutoZoomController()
        self.visualizer = AutoZoomVisualizer()
        self.enhanced_visualizer = EnhancedTrackingVisualizer()
        
        # Initialize calibration
        self.calibrator = PTZCameraCalibrator(camera_ip, camera_username, camera_password)
        
        # Initialize PTZ controller
        self.ptz_controller = create_ptz_controller(
            camera_ip=camera_ip,
            camera_username=camera_username,
            camera_password=camera_password,
            prefer_real_camera=use_real_camera
        )
        
        # Demo state
        self.running = False
        self.frame_count = 0
        self.fps_tracker = []
        self.zoom_history = []
        self.use_enhanced_visualization = True
        
        # Performance tracking
        self.total_processing_time = 0.0
        self.detection_times = []
        self.tracking_times = []
        self.zoom_times = []
        
        logger.info("✅ Real PTZ AutoZoom Demo initialized")
    
    def setup_camera_position(self) -> bool:
        """Setup camera to optimal position using calibration"""
        
        logger.info("📐 Setting up camera position...")
        
        # Apply calibration if available
        if self.calibrator.calibration_file.exists():
            logger.info("📂 Found calibration file, applying position...")
            success = self.calibrator.apply_calibration(self.ptz_controller)
            if success:
                logger.info("✅ Camera positioned using calibration")
                return True
            else:
                logger.warning("⚠️ Failed to apply calibration, using current position")
        else:
            logger.info("📐 No calibration found, setting current position as home")
            
        # Set current position as home
        if self.ptz_controller.set_home_position():
            logger.info("✅ Home position set")
            return True
        else:
            logger.warning("⚠️ Failed to set home position")
            return False
    
    def run_calibration_mode(self):
        """Run interactive camera calibration"""
        
        logger.info("📐 Starting camera calibration mode...")
        
        if not self.ptz_controller.state.camera_connected:
            print("❌ Camera not connected. Cannot calibrate.")
            return False
        
        print("📐 Camera Calibration Mode")
        print("=" * 40)
        print("This will help you set up the optimal camera position")
        print("for your constrained environment.")
        print("=" * 40)
        
        # Run calibration
        self.calibrator.run_interactive_calibration()
        
        # Apply the calibration
        return self.setup_camera_position()
    
    def run_live_demo(self, output_path: Optional[str] = None, display: bool = True):
        """
        Run live demo with real PTZ camera
        
        Args:
            output_path: Optional path to save output video
            display: Whether to display video window
        """
        
        logger.info("🚀 Starting live PTZ camera demo...")
        
        # Get video stream from PTZ camera
        video_stream = self.ptz_controller.get_video_feed()
        
        if not video_stream:
            logger.error("❌ No video stream available from PTZ camera")
            logger.info("🔄 Falling back to simulation mode...")
            self.ptz_controller.switch_to_simulation()
            
            # For simulation, we need a test video
            test_video_path = Path(__file__).parent.parent / "autozoom_consolidated_demo" / "videos" / "test_input.mp4"
            if test_video_path.exists():
                video_stream = cv2.VideoCapture(str(test_video_path))
            else:
                logger.error("❌ No test video available for simulation")
                return
        
        # Initialize video writer if output path specified
        video_writer = None
        if output_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            fps = 30
            video_writer = cv2.VideoWriter(output_path, fourcc, fps, (640, 480))
        
        # Setup camera position
        self.setup_camera_position()
        
        self.running = True
        logger.info("✅ Demo started")
        
        # Print enhanced controls
        print("\n🎮 Enhanced Demo Controls:")
        print("  q - Quit demo")
        print("  r - Reset zoom to home position")
        print("  h - Set current position as home")
        print("  s - Switch to simulation mode")
        print("  c - Reconnect to camera")
        print("  v - Toggle enhanced visualization")
        print("  p - Toggle path visualization")
        print("  m - Toggle metrics display")
        print("  d - Toggle debug information")
        print("  SPACE - Clear track paths")
        print("  1-9 - Zoom level shortcuts")
        print("=" * 50)
        
        try:
            while self.running:
                # Read frame
                ret, frame = video_stream.read()
                if not ret:
                    logger.warning("⚠️ No frame received from video stream")
                    break
                
                # Process frame
                processed_frame = self._process_frame(frame)
                
                # Write output frame
                if video_writer:
                    video_writer.write(processed_frame)
                
                # Display frame
                if display:
                    cv2.imshow('Real PTZ AutoZoom Demo', processed_frame)
                    
                    # Handle keyboard input
                    key = cv2.waitKey(1) & 0xFF
                    if key == ord('q'):
                        logger.info("🛑 Quit requested")
                        break
                    elif key == ord('r'):
                        logger.info("🔄 Resetting zoom")
                        self.ptz_controller.reset_zoom()
                    elif key == ord('h'):
                        logger.info("🏠 Setting home position")
                        self.ptz_controller.set_home_position()
                    elif key == ord('s'):
                        logger.info("🔄 Switching to simulation mode")
                        self.ptz_controller.switch_to_simulation()
                    elif key == ord('c'):
                        logger.info("🔄 Reconnecting to camera")
                        self.ptz_controller.reconnect_camera()
                    elif key == ord('v'):
                        logger.info("🎨 Toggling enhanced visualization")
                        self.use_enhanced_visualization = not self.use_enhanced_visualization
                    elif key == ord('p'):
                        logger.info("🛤️ Toggling path visualization")
                        self.enhanced_visualizer.toggle_paths()
                    elif key == ord('m'):
                        logger.info("📊 Toggling metrics display")
                        self.enhanced_visualizer.toggle_metrics()
                    elif key == ord('d'):
                        logger.info("🐛 Toggling debug information")
                        self.enhanced_visualizer.toggle_debug()
                    elif key == ord(' '):
                        logger.info("🧹 Clearing track paths")
                        self.enhanced_visualizer.clear_paths()
                    elif key >= ord('1') and key <= ord('9'):
                        zoom_level = key - ord('0')
                        logger.info(f"🔍 Manual zoom to level {zoom_level}")
                        self._apply_manual_zoom(zoom_level)
                
                self.frame_count += 1
                
                # Update FPS every 30 frames
                if self.frame_count % 30 == 0:
                    self._log_performance_stats()
                    
        except KeyboardInterrupt:
            logger.info("🛑 Demo interrupted by user")
        except Exception as e:
            logger.error(f"❌ Demo error: {e}")
        finally:
            # Cleanup
            self.running = False
            
            if video_writer:
                video_writer.release()
            
            if video_stream:
                video_stream.release()
            
            if display:
                cv2.destroyAllWindows()
            
            # Return PTZ camera to home position
            self.ptz_controller.reset_zoom()
            self.ptz_controller.cleanup()
            
            logger.info("✅ Demo completed")
    
    def _process_frame(self, frame: np.ndarray) -> np.ndarray:
        """Process single frame through the AutoZoom pipeline"""
        
        frame_start_time = time.time()
        
        # 1. Detection
        detection_start = time.time()
        detections = self.detector.detect(frame)
        detection_time = time.time() - detection_start
        self.detection_times.append(detection_time)
        
        # 2. Tracking
        tracking_start = time.time()
        tracks = self.tracker.update(detections, frame)
        tracking_time = time.time() - tracking_start
        self.tracking_times.append(tracking_time)
        
        # 3. Confidence monitoring
        self.confidence_monitor.update(tracks)
        
        # 4. AutoZoom decision
        zoom_start = time.time()
        zoom_target, zoom_active, zoom_info = self.autozoom_controller.update(
            tracks, frame_start_time
        )
        
        # 5. Apply zoom (real camera or simulation)
        if zoom_active and zoom_target:
            # Calculate zoom parameters
            zoom_bbox = (
                int(zoom_target.bbox[0]),
                int(zoom_target.bbox[1]),
                int(zoom_target.bbox[2]),
                int(zoom_target.bbox[3])
            )
            
            # Apply zoom using PTZ controller
            zoomed_frame = self.ptz_controller.apply_zoom(
                frame=frame,
                zoom_level=2.0,
                zoom_bbox=zoom_bbox,
                target_track_id=zoom_target.track_id
            )
        else:
            # No zoom - return to normal view
            zoomed_frame = self.ptz_controller.apply_zoom(
                frame=frame,
                zoom_level=1.0
            )
        
        zoom_time = time.time() - zoom_start
        self.zoom_times.append(zoom_time)
        
        # 6. Visualization - USE ADVANCED DEMO FRONTEND
        from advanced_demo_frontend import AdvancedDemoFrontend, AdvancedTrack
        
        if not hasattr(self, 'advanced_frontend'):
            self.advanced_frontend = AdvancedDemoFrontend()
        
        # Convert tracks to advanced tracks format
        advanced_tracks = []
        for track in tracks:
            # Get face detection if available
            face_bbox = None
            face_confidence = 0.0
            
            # Check if track has face features (helmet proxy)
            if hasattr(track, 'helmet_features') and track.helmet_features:
                face_bbox = track.helmet_features.get('bbox')
                face_confidence = track.helmet_features.get('confidence', 0.0)
            
            advanced_track = AdvancedTrack(
                track_id=track.track_id,
                bbox=track.bbox,
                confidence=getattr(track, 'confidence', 0.5),
                energy=getattr(track, 'energy', 0.0),
                age=getattr(track, 'age', 0),
                face_bbox=face_bbox,
                face_confidence=face_confidence,
                helmet_detected=face_confidence > 0.6,
                helmet_confidence=face_confidence,
                velocity=getattr(track, 'velocity', (0.0, 0.0)),
                zoom_priority=getattr(track, 'priority_score', 0.0)
            )
            advanced_tracks.append(advanced_track)
        
        # Enhanced zoom info
        enhanced_zoom_info = {
            'is_zooming': zoom_info.get('is_zooming', False),
            'zoom_target': zoom_target if zoom_active else None,
            'zoom_level': self.ptz_controller.state.zoom_level,
            'tracks': advanced_tracks
        }
        
        # Use advanced demo frontend for visualization
        viz_frame = self.advanced_frontend.process_frame_for_demo(
            zoomed_frame, advanced_tracks, detections, enhanced_zoom_info
        )
        
        # Track total processing time
        total_time = time.time() - frame_start_time
        self.total_processing_time += total_time
        
        return viz_frame
    
    def _apply_manual_zoom(self, zoom_level: int):
        """Apply manual zoom level"""
        
        try:
            # Apply zoom to current camera position
            self.ptz_controller.apply_zoom(
                frame=np.zeros((480, 640, 3), dtype=np.uint8),  # Dummy frame
                zoom_level=float(zoom_level),
                zoom_center=(0.5, 0.5)
            )
            logger.info(f"✅ Manual zoom applied: {zoom_level}x")
        except Exception as e:
            logger.error(f"❌ Failed to apply manual zoom: {e}")
    
    def _get_performance_info(self) -> dict:
        """Get performance information for enhanced visualization"""
        
        # Calculate recent averages
        recent_detection_time = np.mean(self.detection_times[-10:]) if self.detection_times else 0.0
        recent_tracking_time = np.mean(self.tracking_times[-10:]) if self.tracking_times else 0.0
        recent_zoom_time = np.mean(self.zoom_times[-10:]) if self.zoom_times else 0.0
        
        return {
            'fps': self._calculate_fps(),
            'detection_time': recent_detection_time,
            'tracking_time': recent_tracking_time,
            'zoom_time': recent_zoom_time,
            'frame_count': self.frame_count,
            'total_processing_time': self.total_processing_time
        }
    
    def _get_additional_info(self) -> dict:
        """Get additional information for visualization"""
        
        ptz_info = self.ptz_controller.get_zoom_info()
        ptz_stats = self.ptz_controller.get_performance_stats()
        
        return {
            'ptz_mode': ptz_info['mode'],
            'camera_connected': ptz_info['camera_connected'],
            'zoom_level': ptz_info['zoom_level'],
            'zoom_target_id': ptz_info.get('zoom_target_id', None),
            'total_zoom_ops': ptz_stats.get('total_zoom_ops', 0),
            'zoom_success_rate': ptz_stats.get('success_rate', 0.0),
            'frame_count': self.frame_count,
            'avg_fps': self._calculate_fps()
        }
    
    def _calculate_fps(self) -> float:
        """Calculate current FPS"""
        if len(self.fps_tracker) < 2:
            return 0.0
        
        if len(self.fps_tracker) > 30:
            self.fps_tracker = self.fps_tracker[-30:]
        
        time_span = self.fps_tracker[-1] - self.fps_tracker[0]
        if time_span > 0:
            return (len(self.fps_tracker) - 1) / time_span
        return 0.0
    
    def _log_performance_stats(self):
        """Log performance statistics"""
        
        if self.frame_count == 0:
            return
        
        avg_fps = self._calculate_fps()
        avg_detection_time = np.mean(self.detection_times[-30:]) if self.detection_times else 0
        avg_tracking_time = np.mean(self.tracking_times[-30:]) if self.tracking_times else 0
        avg_zoom_time = np.mean(self.zoom_times[-30:]) if self.zoom_times else 0
        
        ptz_stats = self.ptz_controller.get_performance_stats()
        
        logger.info(f"📊 Performance Stats (Frame {self.frame_count}):")
        logger.info(f"   FPS: {avg_fps:.1f}")
        logger.info(f"   Detection: {avg_detection_time*1000:.1f}ms")
        logger.info(f"   Tracking: {avg_tracking_time*1000:.1f}ms")
        logger.info(f"   Zoom: {avg_zoom_time*1000:.1f}ms")
        logger.info(f"   PTZ Mode: {self.ptz_controller.state.mode.value}")
        logger.info(f"   Zoom Ops: {ptz_stats.get('total_zoom_ops', 0)}")
        logger.info(f"   Success Rate: {ptz_stats.get('success_rate', 0.0):.1%}")

def main():
    """Main entry point"""
    
    parser = argparse.ArgumentParser(description="Real PTZ AutoZoom Demo")
    parser.add_argument('--camera-ip', default='**************',
                       help='PTZ camera IP address')
    parser.add_argument('--camera-username', default='admin',
                       help='Camera username')
    parser.add_argument('--camera-password', default='admin1234',
                       help='Camera password')
    parser.add_argument('--output', type=str,
                       help='Output video path')
    parser.add_argument('--no-display', action='store_true',
                       help='Run without display window')
    parser.add_argument('--simulation', action='store_true',
                       help='Use simulation mode instead of real camera')
    parser.add_argument('--calibrate', action='store_true',
                       help='Run camera calibration mode first')
    parser.add_argument('--enhanced-viz', action='store_true', default=True,
                       help='Use enhanced visualization (default: True)')
    parser.add_argument('--log-level', default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='Logging level')
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("🎯 Real PTZ AutoZoom Demo")
    print("=" * 50)
    print(f"Camera IP: {args.camera_ip}")
    print(f"Camera User: {args.camera_username}")
    print(f"Mode: {'Simulation' if args.simulation else 'Real Camera'}")
    print(f"Calibration: {'Yes' if args.calibrate else 'No'}")
    print(f"Enhanced Viz: {'Yes' if args.enhanced_viz else 'No'}")
    print(f"Output: {args.output or 'None'}")
    print(f"Display: {'No' if args.no_display else 'Yes'}")
    print("=" * 50)
    
    # Create demo instance
    demo = RealPTZAutoZoomDemo(
        camera_ip=args.camera_ip,
        camera_username=args.camera_username,
        camera_password=args.camera_password,
        use_real_camera=not args.simulation
    )
    
    # Set visualization mode
    demo.use_enhanced_visualization = args.enhanced_viz
    
    # Run calibration if requested
    if args.calibrate and not args.simulation:
        print("\n📐 Running Camera Calibration...")
        if not demo.run_calibration_mode():
            print("❌ Calibration failed or cancelled")
            return
        print("✅ Calibration completed\n")
    
    # Run demo
    try:
        demo.run_live_demo(
            output_path=args.output,
            display=not args.no_display
        )
    except KeyboardInterrupt:
        print("\n🛑 Demo interrupted by user")
    except Exception as e:
        print(f"❌ Demo error: {e}")
        logger.error(f"Demo error: {e}", exc_info=True)
    
    print("🏁 Demo completed")

if __name__ == "__main__":
    main()