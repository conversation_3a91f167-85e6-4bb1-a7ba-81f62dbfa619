#!/usr/bin/env python3
"""
🎯 Static Demo Showcase
======================

Shows ALL advanced features on a static background - no camera needed!
Perfect for demonstrating:
- Track IDs with special bboxes
- Face detection and zoom-on-face
- Thresholds and mathematical framework
- Real-time data overlays

Built with love for worker protection and safety! 💙
"""

import cv2
import numpy as np
import time
import sys
import os

# Add paths
sys.path.append(os.path.dirname(__file__))

# Import demo components
from advanced_demo_frontend import AdvancedDemoFrontend, AdvancedTrack

def create_demo_background():
    """Create a demo background scene"""
    
    # Create a construction site-like background
    frame = np.ones((480, 640, 3), dtype=np.uint8) * 50  # Dark gray background
    
    # Add some construction elements
    # Ground
    cv2.rectangle(frame, (0, 350), (640, 480), (139, 69, 19), -1)  # Brown ground
    
    # Building outline
    cv2.rectangle(frame, (450, 100), (620, 350), (128, 128, 128), 2)  # Building
    cv2.rectangle(frame, (470, 120), (500, 150), (255, 255, 0), -1)   # Window
    cv2.rectangle(frame, (520, 120), (550, 150), (255, 255, 0), -1)   # Window
    
    # Safety zone
    cv2.rectangle(frame, (50, 200), (400, 350), (0, 255, 0), 2)       # Safety zone
    
    # Add some texture
    for i in range(0, 640, 50):
        cv2.line(frame, (i, 350), (i, 480), (160, 82, 45), 1)
    
    # Add title
    cv2.putText(frame, "CONSTRUCTION SITE - SAFETY MONITORING", (80, 40), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
    
    return frame

def main():
    """Main static demo"""
    
    print("🎯 Static Demo Showcase - ALL ADVANCED FEATURES")
    print("=" * 60)
    print("🔥 Demonstrating:")
    print("✅ Large Track IDs with special corner-style bounding boxes")
    print("✅ Face detection with zoom-on-face crosshairs")
    print("✅ Confidence bars with threshold indicators")
    print("✅ Mathematical framework overlay (E ∝ zoom²)")
    print("✅ Real-time performance metrics panel")
    print("✅ Threshold settings display")
    print("✅ System status indicators")
    print("✅ Zoom targeting with priority scoring")
    print("=" * 60)
    print("Controls:")
    print("  q - Quit demo")
    print("  f - Toggle face detection")
    print("  t - Toggle thresholds")
    print("  m - Toggle mathematical overlay")
    print("  SPACE - Randomize tracks")
    print("  1-9 - Simulate zoom levels")
    print("=" * 60)
    
    # Initialize demo components
    frontend = AdvancedDemoFrontend()
    
    # Create demo background
    background = create_demo_background()
    
    # Create realistic demo tracks
    tracks = [
        AdvancedTrack(
            track_id=1,
            bbox=(120, 180, 200, 320),
            confidence=0.87,
            energy=1347.2,
            age=45,
            face_bbox=(135, 185, 185, 225),
            face_confidence=0.82,
            helmet_detected=True,
            helmet_confidence=0.89,
            velocity=(1.2, -0.8),
            zoom_priority=0.35
        ),
        AdvancedTrack(
            track_id=2,
            bbox=(280, 200, 360, 340),
            confidence=0.41,  # LOW CONFIDENCE - ZOOM TARGET
            energy=756.8,
            age=23,
            face_bbox=(295, 205, 345, 245),
            face_confidence=0.28,  # LOW FACE CONFIDENCE
            helmet_detected=False,
            helmet_confidence=0.15,
            velocity=(-0.5, 1.2),
            zoom_priority=0.92  # HIGH PRIORITY FOR ZOOM
        ),
        AdvancedTrack(
            track_id=3,
            bbox=(480, 160, 560, 300),
            confidence=0.76,
            energy=1456.3,
            age=67,
            face_bbox=(495, 165, 545, 205),
            face_confidence=0.91,
            helmet_detected=True,
            helmet_confidence=0.94,
            velocity=(0.3, -1.8),
            zoom_priority=0.22
        ),
        AdvancedTrack(
            track_id=4,
            bbox=(340, 120, 410, 250),
            confidence=0.63,
            energy=1123.7,
            age=12,
            face_bbox=(355, 125, 395, 155),
            face_confidence=0.67,
            helmet_detected=True,
            helmet_confidence=0.71,
            velocity=(-2.1, 0.9),
            zoom_priority=0.45
        )
    ]
    
    # Animation parameters
    frame_count = 0
    start_time = time.time()
    zoom_simulation = False
    zoom_start_time = 0
    
    try:
        while True:
            # Create frame
            frame = background.copy()
            
            # Animate tracks
            current_time = time.time()
            for track in tracks:
                # Animate position slightly
                vx, vy = track.velocity
                x1, y1, x2, y2 = track.bbox
                
                # Small movement
                offset_x = np.sin(current_time + track.track_id) * 2
                offset_y = np.cos(current_time + track.track_id * 1.5) * 1.5
                
                new_x1 = x1 + offset_x
                new_y1 = y1 + offset_y
                new_x2 = x2 + offset_x
                new_y2 = y2 + offset_y
                
                # Keep in bounds
                if new_x1 > 0 and new_x2 < 640 and new_y1 > 0 and new_y2 < 480:
                    track.bbox = (new_x1, new_y1, new_x2, new_y2)
                    
                    # Update face bbox too
                    if track.face_bbox:
                        fx1, fy1, fx2, fy2 = track.face_bbox
                        track.face_bbox = (fx1 + offset_x, fy1 + offset_y, fx2 + offset_x, fy2 + offset_y)
                
                # Animate confidence
                track.confidence += np.sin(current_time * 2 + track.track_id) * 0.005
                track.confidence = np.clip(track.confidence, 0.2, 0.95)
                
                # Animate energy
                track.energy += np.cos(current_time * 3 + track.track_id) * 20
                track.energy = max(200, min(2000, track.energy))
            
            # Simulate zoom operation on track 2 (low confidence)
            zoom_info = {
                'is_zooming': tracks[1].confidence < 0.5,
                'zoom_target': tracks[1] if tracks[1].confidence < 0.5 else None,
                'zoom_level': 2.8 if tracks[1].confidence < 0.5 else 1.0,
                'tracks': tracks
            }
            
            # Process frame with advanced frontend
            demo_frame = frontend.process_frame_for_demo(frame, tracks, [], zoom_info)
            
            # Add demo information
            demo_info = [
                "🎯 ADVANCED AUTOZOOM DEMO - ALL FEATURES ACTIVE",
                f"⏱️  Runtime: {current_time - start_time:.1f}s | Frames: {frame_count}",
                "🎮 Press 'q' to quit, 'f'/'t'/'m' to toggle features, SPACE to randomize"
            ]
            
            for i, info in enumerate(demo_info):
                cv2.putText(demo_frame, info, (10, demo_frame.shape[0] - 60 + i * 20), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
            
            # Display frame
            cv2.imshow('🎯 Advanced AutoZoom Demo - ALL FEATURES SHOWCASE', demo_frame)
            
            # Handle keyboard input
            key = cv2.waitKey(30) & 0xFF  # 30ms delay for smooth animation
            
            if key == ord('q'):
                print("🛑 Demo stopped")
                break
            elif key == ord('f'):
                frontend.toggle_feature('face_detection')
                print("🔄 Toggled face detection display")
            elif key == ord('t'):
                frontend.toggle_feature('thresholds')
                print("🔄 Toggled threshold display")
            elif key == ord('m'):
                frontend.toggle_feature('mathematical')
                print("🔄 Toggled mathematical framework display")
            elif key == ord(' '):
                # Randomize track positions and properties
                for track in tracks:
                    track.bbox = (
                        np.random.randint(50, 500),
                        np.random.randint(100, 250),
                        np.random.randint(50, 500) + 80,
                        np.random.randint(100, 250) + 120
                    )
                    track.confidence = np.random.uniform(0.3, 0.95)
                    track.energy = np.random.uniform(400, 1800)
                print("🔄 Randomized track positions and properties")
            elif key >= ord('1') and key <= ord('9'):
                zoom_level = key - ord('0')
                print(f"🔍 Simulating zoom level: {zoom_level}x")
                # Could add zoom level simulation here
            
            frame_count += 1
            
            # Show performance stats every 60 frames
            if frame_count % 60 == 0:
                elapsed = current_time - start_time
                fps = frame_count / elapsed
                print(f"📊 Demo performance: {fps:.1f} FPS | Tracks: {len(tracks)} | Features: ALL ACTIVE")
                
    except KeyboardInterrupt:
        print("\n🛑 Demo interrupted")
    
    finally:
        cv2.destroyAllWindows()
        print("✅ Static Demo Showcase completed")

if __name__ == "__main__":
    main()