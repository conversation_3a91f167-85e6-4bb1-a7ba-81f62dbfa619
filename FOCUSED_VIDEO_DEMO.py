#!/usr/bin/env python3
"""
🎯 Focused Video Demo - Masks and Headphones
============================================

Enhanced video demo that focuses on people wearing masks and headphones.
Perfect for proving your algorithms work with specific detection targets.

Built with love for worker protection and safety! 💙
"""

import subprocess
import sys
import os
import cv2
import numpy as np
import time
import logging
from pathlib import Path

# Add paths to access modules
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'autozoom_consolidated_demo'))

from core.mask_headphone_detector import MaskHeadphoneDetector
from core.advanced_demo_frontend import AdvancedDemoFrontend, AdvancedTrack
from tracker import EnergyBasedTracker
from confidence_monitor import ConfidenceMonitor
from autozoom_advanced import AdvancedAutoZoomController
from zoom_simulator import PTZZoomSimulator

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FocusedVideoDemo:
    """
    🎯 Focused Video Demo
    
    Demonstrates AutoZoom with specific focus on people wearing masks and headphones.
    Shows track IDs, mathematical framework, and advanced visualizations.
    """
    
    def __init__(self):
        """Initialize the focused video demo"""
        
        logger.info("🎯 Initializing Focused Video Demo...")
        
        # Initialize components
        self.detector = MaskHeadphoneDetector()
        self.tracker = EnergyBasedTracker()
        self.confidence_monitor = ConfidenceMonitor()
        self.autozoom_controller = AdvancedAutoZoomController()
        self.zoom_simulator = PTZZoomSimulator()
        self.frontend = AdvancedDemoFrontend()
        
        # Demo state
        self.running = False
        self.frame_count = 0
        self.detection_stats = {
            'total_people': 0,
            'mask_wearers': 0,
            'headphone_wearers': 0,
            'both': 0
        }
        
        logger.info("✅ Focused Video Demo initialized")
    
    def run_demo(self, video_path: str, output_path: str = None, no_display: bool = False):
        """
        Run the focused video demo

        Args:
            video_path: Path to video file
            output_path: Optional output video path
            no_display: Run without display window
        """
        
        logger.info(f"🎬 Starting focused video demo with {video_path}")
        
        # Open video
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            logger.error(f"❌ Could not open video: {video_path}")
            return
        
        # Get video properties
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        logger.info(f"📹 Video: {width}x{height}, {fps} FPS, {total_frames} frames")
        
        # Setup output video if specified
        video_writer = None
        if output_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            video_writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        # Demo info
        print("\\n🎯 FOCUSED VIDEO DEMO - MASKS AND HEADPHONES")
        print("=" * 70)
        print("🔍 Focusing on people wearing masks and headphones")
        print("📊 Showing track IDs, mathematical framework, and advanced features")
        print("🎮 Controls:")
        print("  q - Quit demo")
        print("  SPACE - Pause/Resume")
        print("  r - Reset tracking")
        print("  s - Show/Hide statistics")
        print("=" * 70)
        
        self.running = True
        paused = False
        show_stats = True
        start_time = time.time()
        
        try:
            while self.running:
                if not paused:
                    ret, frame = cap.read()
                    if not ret:
                        logger.info("📹 End of video reached")
                        break
                    
                    # Process frame
                    processed_frame = self._process_frame(frame)
                    
                    # Add demo information
                    if show_stats:
                        self._add_demo_info(processed_frame, start_time)
                    
                    self.frame_count += 1
                else:
                    processed_frame = frame.copy()
                    cv2.putText(processed_frame, "PAUSED - Press SPACE to resume", 
                               (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)
                
                # Write output frame
                if video_writer:
                    video_writer.write(processed_frame)
                
                # Display frame (if not in no_display mode)
                if not no_display:
                    cv2.imshow('🎯 Focused Video Demo - Masks & Headphones', processed_frame)

                # Handle keyboard input
                key = cv2.waitKey(1) & 0xFF if not no_display else -1
                if key == ord('q'):
                    logger.info("🛑 Quit requested")
                    break
                elif key == ord(' '):
                    paused = not paused
                    logger.info(f"⏸️ {'Paused' if paused else 'Resumed'}")
                elif key == ord('r'):
                    logger.info("🔄 Resetting tracking")
                    self.tracker = EnergyBasedTracker()
                    self.confidence_monitor = ConfidenceMonitor()
                elif key == ord('s'):
                    show_stats = not show_stats
                    logger.info(f"📊 Statistics {'shown' if show_stats else 'hidden'}")
                
                # Log stats every 100 frames
                if self.frame_count % 100 == 0:
                    self._log_stats()
                    
        except KeyboardInterrupt:
            logger.info("🛑 Demo interrupted by user")
        
        finally:
            # Cleanup
            self.running = False
            cap.release()
            if video_writer:
                video_writer.release()
            cv2.destroyAllWindows()
            
            # Final stats
            self._log_final_stats(start_time)
            logger.info("✅ Focused video demo completed")
    
    def _process_frame(self, frame: np.ndarray) -> np.ndarray:
        """Process a single frame through the focused detection pipeline"""
        
        # 1. Detect people with masks and headphones
        mask_headphone_detections = self.detector.detect(frame)
        
        # Update detection statistics
        self._update_detection_stats(mask_headphone_detections)
        
        # Convert to standard detections for tracking
        standard_detections = self.detector.convert_to_standard_detections(mask_headphone_detections)
        
        # 2. Update tracking
        tracks = self.tracker.update(standard_detections, frame)
        
        # 3. Update confidence monitoring
        self.confidence_monitor.update(tracks)
        
        # 4. AutoZoom decision (prioritize mask/headphone wearers)
        zoom_target, zoom_active, zoom_info = self.autozoom_controller.update(
            tracks, time.time()
        )

        # 5. Apply zoom simulation if needed - THIS IS THE KEY MISSING STEP!
        if zoom_active and zoom_target:
            # Apply actual zoom to the frame
            zoom_level = 2.0  # 2x zoom for mask/headphone wearers
            zoom_center = None
            zoom_bbox = None

            # Get zoom center from target
            if hasattr(zoom_target, 'center'):
                zoom_center = zoom_target.center
            elif hasattr(zoom_target, 'bbox'):
                # Calculate center from bbox
                x1, y1, x2, y2 = zoom_target.bbox
                center_x = (x1 + x2) / 2 / frame.shape[1]  # Normalize to 0-1
                center_y = (y1 + y2) / 2 / frame.shape[0]  # Normalize to 0-1
                zoom_center = (center_x, center_y)
                zoom_bbox = zoom_target.bbox

            # Apply the zoom!
            zoomed_frame = self.zoom_simulator.apply_zoom(
                frame, zoom_level, zoom_center, zoom_bbox
            )
            logger.info(f"🔍 ZOOMING on target with mask/headphones! Level: {zoom_level}x")
        else:
            zoomed_frame = frame
            self.zoom_simulator.reset_zoom()

        # 6. Transform tracks for zoomed view (if zoomed)
        zoom_sim_info = self.zoom_simulator.get_zoom_info()
        if zoom_sim_info['is_zoomed']:
            # Transform track coordinates to match zoomed view
            transformed_tracks = []
            for track in tracks:
                transformed_bbox = self.zoom_simulator.transform_coordinates_to_zoomed_view(
                    track.bbox, zoom_sim_info['crop_region']
                )
                if transformed_bbox is not None:
                    # Create transformed track
                    transformed_track = track
                    transformed_track.bbox = transformed_bbox
                    transformed_tracks.append(transformed_track)
            tracks = transformed_tracks

        # 7. Convert tracks to advanced tracks for visualization
        advanced_tracks = self._convert_to_advanced_tracks(tracks, mask_headphone_detections)

        # 8. Enhanced zoom info
        enhanced_zoom_info = {
            'is_zooming': zoom_active,
            'zoom_target': zoom_target if zoom_active else None,
            'zoom_level': 2.0 if zoom_active else 1.0,
            'tracks': advanced_tracks,
            'focused_detection': True,
            'detection_stats': self.detection_stats,
            'zoom_applied': zoom_active  # Flag to show zoom was actually applied
        }

        # 9. Use advanced frontend for visualization on the ZOOMED frame
        viz_frame = self.frontend.process_frame_for_demo(
            zoomed_frame, advanced_tracks, standard_detections, enhanced_zoom_info
        )
        
        return viz_frame
    
    def _convert_to_advanced_tracks(self, tracks, mask_headphone_detections):
        """Convert tracks to advanced tracks with mask/headphone info"""
        
        advanced_tracks = []
        
        for track in tracks:
            # Find corresponding mask/headphone detection
            mask_info = None
            for detection in mask_headphone_detections:
                # Check if this detection matches the track (by bbox overlap)
                if self._bbox_overlap(track.bbox, detection.bbox) > 0.7:
                    mask_info = detection
                    break
            
            # Create advanced track
            advanced_track = AdvancedTrack(
                track_id=track.track_id,
                bbox=track.bbox,
                confidence=getattr(track, 'confidence', 0.5),
                energy=getattr(track, 'energy', 0.0),
                age=getattr(track, 'age', 0),
                face_bbox=None,
                face_confidence=0.0,
                helmet_detected=mask_info.has_mask if mask_info else False,
                helmet_confidence=mask_info.mask_confidence if mask_info else 0.0,
                velocity=getattr(track, 'velocity', (0.0, 0.0)),
                zoom_priority=getattr(track, 'priority_score', 0.0)
            )
            
            # Add mask/headphone metadata
            if mask_info:
                advanced_track.metadata = {
                    'has_mask': mask_info.has_mask,
                    'has_headphones': mask_info.has_headphones,
                    'person_type': mask_info.person_type,
                    'mask_confidence': mask_info.mask_confidence,
                    'headphone_confidence': mask_info.headphone_confidence,
                    'is_priority_target': mask_info.person_type != 'neither'
                }
            
            advanced_tracks.append(advanced_track)
        
        return advanced_tracks
    
    def _bbox_overlap(self, bbox1, bbox2):
        """Calculate overlap between two bounding boxes"""
        x1, y1, x2, y2 = bbox1
        x3, y3, x4, y4 = bbox2
        
        # Calculate intersection area
        ix1 = max(x1, x3)
        iy1 = max(y1, y3)
        ix2 = min(x2, x4)
        iy2 = min(y2, y4)
        
        if ix2 <= ix1 or iy2 <= iy1:
            return 0.0
        
        intersection = (ix2 - ix1) * (iy2 - iy1)
        area1 = (x2 - x1) * (y2 - y1)
        area2 = (x4 - x3) * (y4 - y3)
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0
    
    def _update_detection_stats(self, detections):
        """Update detection statistics"""
        
        self.detection_stats['total_people'] = len(detections)
        self.detection_stats['mask_wearers'] = sum(1 for d in detections if d.has_mask)
        self.detection_stats['headphone_wearers'] = sum(1 for d in detections if d.has_headphones)
        self.detection_stats['both'] = sum(1 for d in detections if d.has_mask and d.has_headphones)
    
    def _add_demo_info(self, frame, start_time):
        """Add demo information overlay"""
        
        # Demo title
        cv2.putText(frame, "FOCUSED DEMO: MASKS & HEADPHONES", (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
        
        # Runtime info
        runtime = time.time() - start_time
        cv2.putText(frame, f"Runtime: {runtime:.1f}s | Frame: {self.frame_count}", 
                   (10, frame.shape[0] - 60), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # Detection stats
        stats = self.detection_stats
        stats_text = f"People: {stats['total_people']} | Masks: {stats['mask_wearers']} | Headphones: {stats['headphone_wearers']} | Both: {stats['both']}"
        cv2.putText(frame, stats_text, (10, frame.shape[0] - 40), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        
        # Focus indicator
        cv2.putText(frame, "🎯 FOCUSING ON MASK & HEADPHONE WEARERS", 
                   (10, frame.shape[0] - 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
    
    def _log_stats(self):
        """Log current statistics"""
        
        stats = self.detection_stats
        logger.info(f"📊 Frame {self.frame_count}: People={stats['total_people']}, "
                   f"Masks={stats['mask_wearers']}, Headphones={stats['headphone_wearers']}, "
                   f"Both={stats['both']}")
    
    def _log_final_stats(self, start_time):
        """Log final statistics"""
        
        runtime = time.time() - start_time
        fps = self.frame_count / runtime if runtime > 0 else 0
        
        logger.info("🎉 DEMO COMPLETED")
        logger.info(f"📊 Final Stats:")
        logger.info(f"   Runtime: {runtime:.1f}s")
        logger.info(f"   Frames processed: {self.frame_count}")
        logger.info(f"   Average FPS: {fps:.1f}")
        logger.info(f"   Final detection counts: {self.detection_stats}")

def main():
    """Main function"""
    
    print("🎯 FOCUSED VIDEO DEMO - MASKS AND HEADPHONES")
    print("=" * 70)
    print("🔍 This demo focuses on people wearing masks and headphones")
    print("📊 Shows all advanced features: Track IDs, math framework, visualizations")
    print("🎬 Perfect for proving your algorithms work!")
    print("=" * 70)
    
    # Available videos
    videos = [
        "/Users/<USER>/Desktop/Demo-thursday/videos-for-testinig/test7.mp4",
        "/Users/<USER>/Desktop/Demo-thursday/videos-for-testinig/test2.mp4",
        "/Users/<USER>/Desktop/Demo-thursday/videos-for-testinig/test4.mp4",
        "/Users/<USER>/Desktop/Demo-thursday/videos-for-testinig/new_york_test.mp4"
    ]
    
    print("Available videos:")
    for i, video in enumerate(videos, 1):
        print(f"  {i}. {video.split('/')[-1]}")
    
    # Choose video
    try:
        choice = input("\\nSelect video (1-4) or press Enter for test7: ").strip()
        if choice == "":
            choice = "1"
        video_path = videos[int(choice) - 1]
    except:
        video_path = videos[0]  # Default to test7
    
    print(f"🎬 Selected: {video_path.split('/')[-1]}")
    
    # Ask for output
    save_output = input("Save output video? (y/n): ").strip().lower()
    output_path = None
    if save_output == 'y':
        output_path = f"focused_demo_output_{int(time.time())}.mp4"
        print(f"💾 Output will be saved to: {output_path}")
    
    # Run demo
    demo = FocusedVideoDemo()
    demo.run_demo(video_path, output_path)
    
    print("\\n🎉 Demo completed!")
    if output_path:
        print(f"📄 Output saved to: {output_path}")

if __name__ == "__main__":
    main()