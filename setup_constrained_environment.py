#!/usr/bin/env python3
"""
🎯 Constrained Environment Setup
===============================

Setup script for configuring camera constraints in your specific environment.
Prevents zoom loops and ensures safe camera operation.

Built with love for worker protection and safety! 💙
"""

import sys
import os
import json
import logging
from pathlib import Path

# Add paths to access modules
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'autozoom_consolidated_demo'))

from core.constrained_environment import ConstrainedEnvironmentHandler
from core.ptz_controller import create_ptz_controller

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_environment_config_for_user():
    """Create constrained environment configuration for the user's setup"""
    
    print("🎯 Constrained Environment Setup")
    print("=" * 60)
    print("This setup will prevent zoom loops and ensure safe camera operation.")
    print("Based on your description of blocked objects and walls.")
    print("=" * 60)
    
    # Configuration for user's specific setup
    config = {
        "blocked_zones": [
            # Areas where camera shouldn't zoom due to blocked objects
            {
                "pan": (-45, -15),  # Left side blocked by objects
                "tilt": (-10, 20),
                "description": "Left side blocked by objects"
            },
            {
                "pan": (35, 65),    # Right side blocked by walls
                "tilt": (-5, 25),
                "description": "Right side blocked by walls"
            },
            {
                "pan": (-20, 20),   # Center area too close - blocked view
                "tilt": (-20, -5),
                "description": "Too close - blocked view"
            }
        ],
        "safe_zones": [
            # Areas with good visibility
            {
                "pan": (-15, 35),   # Main work area with good visibility
                "tilt": (-5, 25),
                "description": "Main work area with good visibility"
            },
            {
                "pan": (0, 25),     # Central viewing area
                "tilt": (5, 20),
                "description": "Central viewing area"
            }
        ],
        "optimal_position": {
            "pan": 15.0,        # Best overall position
            "tilt": 10.0,       # Good angle for monitoring
            "zoom": 1.0,        # Start at normal zoom
            "description": "Optimal position for best visibility in constrained environment"
        },
        "max_zoom_level": 3.0,  # Conservative max zoom to prevent getting stuck
        "min_zoom_level": 1.0,
        "zoom_increment": 0.5,
        "max_zoom_attempts": 3,
        "return_to_optimal_timeout": 10.0
    }
    
    return config

def setup_constrained_environment():
    """Setup the constrained environment configuration"""
    
    print("🔧 Setting up constrained environment...")
    
    # Create configuration
    config = create_environment_config_for_user()
    
    # Initialize constrained environment handler
    env_handler = ConstrainedEnvironmentHandler()
    
    # Add blocked zones
    for zone in config["blocked_zones"]:
        env_handler.add_blocked_zone(
            zone["pan"], zone["tilt"], zone["description"]
        )
    
    # Add safe zones
    for zone in config["safe_zones"]:
        env_handler.add_safe_zone(
            zone["pan"], zone["tilt"], zone["description"]
        )
    
    # Set optimal position
    opt_pos = config["optimal_position"]
    env_handler.set_optimal_position(
        opt_pos["pan"], opt_pos["tilt"], opt_pos["zoom"]
    )
    
    # Update configuration
    env_handler.config.update({
        "max_zoom_level": config["max_zoom_level"],
        "min_zoom_level": config["min_zoom_level"],
        "zoom_increment": config["zoom_increment"],
        "max_zoom_attempts": config["max_zoom_attempts"],
        "return_to_optimal_timeout": config["return_to_optimal_timeout"]
    })
    
    # Save configuration
    env_handler.save_config()
    
    print("✅ Constrained environment configuration saved!")
    print(f"   📂 Config file: {env_handler.config_path}")
    print(f"   🚫 Blocked zones: {len(env_handler.blocked_zones)}")
    print(f"   ✅ Safe zones: {len(env_handler.safe_zones)}")
    print(f"   🎯 Optimal position: P:{opt_pos['pan']:.1f}° T:{opt_pos['tilt']:.1f}° Z:{opt_pos['zoom']:.1f}x")
    print(f"   🔍 Zoom range: {config['min_zoom_level']:.1f}x - {config['max_zoom_level']:.1f}x")
    
    return env_handler

def test_camera_connection():
    """Test camera connection and move to optimal position"""
    
    print("\\n🔌 Testing camera connection...")
    
    try:
        # Create PTZ controller
        ptz_controller = create_ptz_controller(
            camera_ip="**************",
            camera_username="admin",
            camera_password="admin1234"
        )
        
        if ptz_controller.state.camera_connected:
            print("✅ Camera connected successfully!")
            
            # Move to optimal position
            print("🏠 Moving camera to optimal position...")
            if ptz_controller.move_to_optimal_position():
                print("✅ Camera positioned at optimal location for best visibility")
            else:
                print("⚠️ Failed to move to optimal position")
            
            # Get status
            status = ptz_controller.get_constrained_environment_status()
            print(f"📊 Environment status: {status}")
            
        else:
            print("❌ Camera connection failed - will use simulation mode")
            print("   The constrained environment is still configured for when camera is available")
        
        # Cleanup
        ptz_controller.cleanup()
        
    except Exception as e:
        print(f"❌ Error testing camera: {e}")

def main():
    """Main setup function"""
    
    print("🎯 AutoZoom Constrained Environment Setup")
    print("=" * 60)
    print("This will configure your camera to work safely in your constrained environment.")
    print("It will prevent zoom loops and ensure the camera always returns to optimal position.")
    print("=" * 60)
    
    try:
        # Setup constrained environment
        env_handler = setup_constrained_environment()
        
        # Test camera connection
        test_camera_connection()
        
        print("\\n🎉 Setup completed successfully!")
        print("=" * 60)
        print("Your camera is now configured for safe operation in your constrained environment.")
        print("The following safety measures are active:")
        print("✅ Blocked zones prevent zoom to obstructed areas")
        print("✅ Safe zones ensure good visibility")
        print("✅ Optimal position provides best overall view")
        print("✅ Conservative zoom limits prevent getting stuck")
        print("✅ Automatic return to optimal position on cleanup")
        print("=" * 60)
        print("🚀 You can now run the demo safely!")
        print("   python REAL_DEMO_NOW.py")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        logger.error(f"Setup error: {e}", exc_info=True)

if __name__ == "__main__":
    main()