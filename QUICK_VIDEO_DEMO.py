#!/usr/bin/env python3
"""
🎯 Quick Video Demo with ALL Features
====================================

Runs your AutoZoom demo on your existing video files with ALL advanced features visible:
- Track IDs with special bounding boxes
- Mathematical framework (E ∝ zoom²)
- Face detection and zoom-on-face
- Thresholds and real-time data

Built with love for worker protection and safety! 💙
"""

import subprocess
import sys

def main():
    """Run demo with video"""
    
    # Available test videos
    videos = [
        "/Users/<USER>/Desktop/Demo-thursday/videos-for-testinig/test7.mp4",
        "/Users/<USER>/Desktop/Demo-thursday/videos-for-testinig/test2.mp4", 
        "/Users/<USER>/Desktop/Demo-thursday/videos-for-testinig/construction-site.mp4"
    ]
    
    print("🎯 Quick Video Demo with ALL Advanced Features")
    print("=" * 60)
    print("Available videos:")
    for i, video in enumerate(videos, 1):
        print(f"  {i}. {video.split('/')[-1]}")
    print("=" * 60)
    
    # Choose video
    try:
        choice = input("Select video (1-3) or press Enter for test7: ").strip()
        if choice == "":
            choice = "1"
        video_path = videos[int(choice) - 1]
    except:
        video_path = videos[0]  # Default to test7
    
    print(f"🎬 Using video: {video_path.split('/')[-1]}")
    print("🔥 ALL advanced features will be shown!")
    
    # Run the demo with the selected video
    cmd = f"""
    cd /Users/<USER>/Desktop/Demo-thursday && \\
    source .venv/bin/activate && \\
    python autozoom_consolidated_demo/main.py \\
        --video "{video_path}" \\
        --enhanced
    """
    
    try:
        subprocess.run(cmd, shell=True, check=True)
    except subprocess.CalledProcessError:
        print("❌ Demo failed. Trying alternative...")
        # Alternative command
        cmd2 = f"""
        cd /Users/<USER>/Desktop/Demo-thursday/autozoom_consolidated_demo && \\
        source ../.venv/bin/activate && \\
        python main.py --video "{video_path}"
        """
        subprocess.run(cmd2, shell=True)

if __name__ == "__main__":
    main()