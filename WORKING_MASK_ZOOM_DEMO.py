#!/usr/bin/env python3
"""
🎯 WORKING MASK & HEADPHONE ZOOM DEMO
====================================

This demo ACTUALLY WORKS and does real zooming on people with masks/headphones!
Based on your successful consolidated demo but with mask/headphone detection.

Key Features:
- REAL zoom operations that you can see
- Fast performance (no slow advanced features)
- Maintains track IDs during zoom
- Energy-based tracking
- Only zooms on mask/headphone wearers

Built with love for worker protection and safety! 💙
"""

import cv2
import numpy as np
import time
import sys
import os
from pathlib import Path
import logging

# Add paths
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'autozoom_consolidated_demo'))

from core.mask_headphone_detector import MaskHeadphoneDetector
from tracker import <PERSON>BasedTracker
from confidence_monitor import ConfidenceMonitor
from autozoom import <PERSON><PERSON>oom<PERSON>ontroller
from zoom_simulator import <PERSON><PERSON><PERSON>oomSimulator
from visualizer import AutoZoomVisualizer

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WorkingMaskZoomDemo:
    """
    🎯 Working Mask & Headphone Zoom Demo
    
    Simple, fast demo that actually zooms on people with masks/headphones.
    Based on the proven consolidated demo architecture.
    """
    
    def __init__(self):
        """Initialize the working demo"""
        
        print("🎯 WORKING MASK & HEADPHONE ZOOM DEMO")
        print("=" * 50)
        print("🔍 This demo ACTUALLY zooms on mask/headphone wearers!")
        print("⚡ Fast performance with real zoom operations")
        print("🎯 Maintains track IDs during zoom")
        print("=" * 50)
        
        # Initialize components (using proven consolidated demo components)
        self.detector = MaskHeadphoneDetector()
        self.tracker = EnergyBasedTracker()
        self.confidence_monitor = ConfidenceMonitor()
        self.autozoom_controller = AutoZoomController()
        self.zoom_simulator = PTZZoomSimulator()
        self.visualizer = AutoZoomVisualizer()
        
        # Demo state
        self.frame_count = 0
        self.start_time = time.time()
        self.fps_history = []
        
        print("✅ All components initialized successfully!")
    
    def run_demo(self, video_path: str, output_path: str = None):
        """Run the working demo"""
        
        print(f"\n🎬 Processing video: {Path(video_path).name}")
        
        # Open video
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"❌ Could not open video: {video_path}")
            return
        
        # Get video properties
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        print(f"📹 Video: {width}x{height} @ {fps}fps, {total_frames} frames")
        
        # Setup output video if requested
        out = None
        if output_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
            print(f"💾 Output will be saved to: {output_path}")
        
        # Process video
        zoom_count = 0
        mask_detections = 0
        headphone_detections = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_start = time.time()
            
            # Process frame
            processed_frame = self._process_frame(frame)
            
            # Track performance
            frame_time = time.time() - frame_start
            fps_current = 1.0 / frame_time if frame_time > 0 else 0
            self.fps_history.append(fps_current)
            
            # Keep only recent FPS history
            if len(self.fps_history) > 30:
                self.fps_history = self.fps_history[-30:]
            
            # Display frame
            cv2.imshow('🎯 Working Mask & Headphone Zoom Demo', processed_frame)
            
            # Save frame if output requested
            if out:
                out.write(processed_frame)
            
            # Handle keyboard input
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                print("🛑 Quit requested")
                break
            elif key == ord(' '):
                print("⏸️ Paused - press any key to continue")
                cv2.waitKey(0)
            
            # Progress update
            self.frame_count += 1
            if self.frame_count % 30 == 0:  # Every second at 30fps
                avg_fps = np.mean(self.fps_history) if self.fps_history else 0
                progress = (self.frame_count / total_frames) * 100
                print(f"📊 {progress:5.1f}% | Frame {self.frame_count:4d}/{total_frames} | "
                      f"FPS: {avg_fps:5.1f} | Zooms: {zoom_count}")
        
        # Cleanup
        cap.release()
        if out:
            out.release()
        cv2.destroyAllWindows()
        
        # Final stats
        total_time = time.time() - self.start_time
        avg_fps = self.frame_count / total_time if total_time > 0 else 0
        
        print(f"\n✅ Demo completed!")
        print(f"📊 Processed {self.frame_count} frames in {total_time:.1f}s")
        print(f"⚡ Average FPS: {avg_fps:.1f}")
        print(f"🔍 Total zoom operations: {zoom_count}")
        
        if output_path and os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024*1024)  # MB
            print(f"💾 Output saved: {output_path} ({file_size:.1f} MB)")
    
    def _process_frame(self, frame: np.ndarray) -> np.ndarray:
        """Process a single frame - FAST and WORKING"""
        
        # 1. Detect people with masks/headphones
        mask_headphone_detections = self.detector.detect(frame)
        
        # Convert to standard detections for tracking
        standard_detections = self.detector.convert_to_standard_detections(mask_headphone_detections)
        
        # 2. Update tracking with energy-based tracker
        tracks = self.tracker.update(standard_detections, frame)
        
        # 3. Update confidence monitoring
        confidence_records = self.confidence_monitor.update(tracks)
        
        # 4. AutoZoom decision - THIS ACTUALLY WORKS!
        autozoom_result = self.autozoom_controller.update(tracks, confidence_records)
        
        # 5. Apply zoom simulation if needed - THE KEY STEP!
        if autozoom_result['is_zooming']:
            zoomed_frame = self.zoom_simulator.apply_zoom(
                frame,
                autozoom_result['zoom_level'],
                autozoom_result['zoom_center'],
                autozoom_result['zoom_bbox']
            )
            print(f"🔍 ZOOMING! Level: {autozoom_result['zoom_level']:.1f}x")
        else:
            zoomed_frame = frame
            self.zoom_simulator.reset_zoom()
        
        # 6. Transform tracks for zoomed view
        zoom_info = self.zoom_simulator.get_zoom_info()
        display_tracks = tracks
        
        if zoom_info['is_zoomed']:
            # Transform track coordinates for zoomed view
            transformed_tracks = []
            for track in tracks:
                transformed_bbox = self.zoom_simulator.transform_coordinates_to_zoomed_view(
                    track.bbox, zoom_info['crop_region']
                )
                if transformed_bbox is not None:
                    # Create transformed track
                    new_track = track
                    new_track.bbox = transformed_bbox
                    transformed_tracks.append(new_track)
            display_tracks = transformed_tracks
        
        # 7. Visualize on the zoomed frame
        viz_frame = self.visualizer.render_frame(
            zoomed_frame, display_tracks, confidence_records, autozoom_result, {}
        )
        
        return viz_frame

def main():
    """Main function"""
    
    # Available videos
    videos = [
        "videos-for-testinig/test7.mp4",
        "videos-for-testinig/test2.mp4",
        "videos-for-testinig/test4.mp4",
        "videos-for-testinig/new_york_test.mp4"
    ]
    
    print("Available videos:")
    for i, video in enumerate(videos, 1):
        video_path = Path(video)
        status = "✅" if video_path.exists() else "❌"
        print(f"  {i}. {video_path.name} {status}")
    
    choice = input("\nSelect video (1-4) or press Enter for test7: ").strip()
    
    if choice.isdigit() and 1 <= int(choice) <= len(videos):
        video_path = videos[int(choice) - 1]
    else:
        video_path = videos[0]  # Default to test7
    
    print(f"🎬 Selected: {Path(video_path).name}")
    
    # Ask for output
    save_output = input("Save output video? (y/n): ").strip().lower() == 'y'
    output_path = None
    if save_output:
        output_path = f"working_mask_zoom_demo_{int(time.time())}.mp4"
    
    # Run demo
    demo = WorkingMaskZoomDemo()
    demo.run_demo(video_path, output_path)

if __name__ == "__main__":
    main()
