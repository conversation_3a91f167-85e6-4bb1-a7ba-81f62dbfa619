#!/usr/bin/env python3
"""
🎯 Mask and Headphone Demo Launcher
==================================

Quick launcher for the focused video demo that detects people with masks and headphones.
Perfect for your pre-recorded video demo where you want to zoom only on specific targets.

Built with love for worker protection and safety! 💙
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def main():
    """Launch the mask and headphone focused demo"""
    
    print("🎯 MASK AND HEADPHONE FOCUSED DEMO")
    print("=" * 50)
    print("🔍 This demo focuses ONLY on people wearing:")
    print("  • Face masks")
    print("  • Headphones") 
    print("  • Both masks and headphones (highest priority)")
    print("")
    print("✅ Features enabled:")
    print("  • Advanced Track ID visualization")
    print("  • Mathematical framework display")
    print("  • Priority-based zoom targeting")
    print("  • Smooth zoom transitions")
    print("  • Detection statistics")
    print("=" * 50)
    
    parser = argparse.ArgumentParser(description="Mask and Headphone Focused Demo")
    parser.add_argument("--video", type=str, 
                       help="Input video path")
    parser.add_argument("--output", type=str,
                       help="Output video path (optional)")
    parser.add_argument("--no-display", action="store_true",
                       help="Run without display window")
    
    args = parser.parse_args()
    
    # Default video options if none provided
    if not args.video:
        print("\n📹 Available test videos:")
        videos = [
            "videos-for-testinig/test7.mp4",
            "videos-for-testinig/test2.mp4", 
            "videos-for-testinig/test4.mp4",
            "videos-for-testinig/new_york_test.mp4"
        ]
        
        for i, video in enumerate(videos, 1):
            video_path = Path(video)
            if video_path.exists():
                print(f"  {i}. {video_path.name} ✅")
            else:
                print(f"  {i}. {video_path.name} ❌ (not found)")
        
        print("\nUsage:")
        print(f"  python {sys.argv[0]} --video videos-for-testinig/test7.mp4")
        print(f"  python {sys.argv[0]} --video videos-for-testinig/test7.mp4 --output results/mask_demo.mp4")
        return
    
    # Check if video exists
    video_path = Path(args.video)
    if not video_path.exists():
        print(f"❌ Error: Video file not found: {args.video}")
        return
    
    # Prepare command
    cmd = [sys.executable, "FOCUSED_VIDEO_DEMO.py"]
    
    # Add video argument (the focused demo will handle it)
    os.environ['DEMO_VIDEO'] = str(video_path.absolute())
    
    if args.output:
        os.environ['DEMO_OUTPUT'] = str(Path(args.output).absolute())
    
    if args.no_display:
        os.environ['DEMO_NO_DISPLAY'] = "1"
    
    print(f"\n🚀 Starting focused demo with video: {video_path.name}")
    print("📋 Controls:")
    print("  q - Quit demo")
    print("  SPACE - Pause/Resume")
    print("  r - Reset statistics")
    print("  v - Toggle visualization features")
    print("=" * 50)
    
    try:
        # Run the focused demo
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Demo failed: {e}")
    except KeyboardInterrupt:
        print("\n🛑 Demo interrupted by user")

if __name__ == "__main__":
    main()
