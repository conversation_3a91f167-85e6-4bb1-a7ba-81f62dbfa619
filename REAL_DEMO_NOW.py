#!/usr/bin/env python3
"""
🎯 REAL DEMO NOW
===============

Launch the real PTZ camera demo with all advanced features immediately.
No complex setup - just shows everything working with real camera.

Built with love for worker protection and safety! 💙
"""

import os
import sys
import subprocess

def main():
    """Launch the real demo immediately"""
    
    print("🎯 LAUNCHING REAL PTZ CAMERA DEMO NOW")
    print("=" * 50)
    print("🔥 Features enabled:")
    print("✅ Real PTZ camera with Dahua integration")
    print("✅ Advanced visualization with Track IDs")
    print("✅ Mathematical framework display (E ∝ zoom²)")
    print("✅ Face detection and zoom-on-face")
    print("✅ Thresholds and confidence indicators")
    print("✅ Automatic return to optimal position")
    print("=" * 50)
    
    # Change to the correct directory
    os.chdir("/Users/<USER>/Desktop/Demo-thursday")
    
    # Activate virtual environment and run demo
    cmd = """
    source .venv/bin/activate && \
    python core/real_ptz_demo.py \
        --camera-ip ************** \
        --camera-username admin \
        --camera-password admin1234 \
        --enhanced-viz
    """
    
    print("🚀 Starting real PTZ camera demo...")
    print("📋 Controls:")
    print("  q - Quit demo")
    print("  r - Reset zoom (return to optimal position)")
    print("  v - Toggle visualization features")
    print("  1-9 - Manual zoom levels")
    print("=" * 50)
    
    try:
        # Run the demo
        subprocess.run(cmd, shell=True, check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Demo failed: {e}")
        print("\n🔄 Trying simulation mode as backup...")
        
        # Fallback to simulation
        cmd_sim = """
        source .venv/bin/activate && \
        python core/real_ptz_demo.py \
            --simulation \
            --enhanced-viz
        """
        subprocess.run(cmd_sim, shell=True)
    except KeyboardInterrupt:
        print("\n🛑 Demo interrupted by user")

if __name__ == "__main__":
    main()